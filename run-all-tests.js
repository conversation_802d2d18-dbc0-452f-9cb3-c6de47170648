#!/usr/bin/env node

/**
 * Cryptocurrency Transaction Monitor Test Runner (Node.js version)
 * Runs tests for all crypto monitor folders (bch*, btc*, nem*, eth*, ltc*)
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m'
};

// Counters
let totalProjects = 0;
let passedProjects = 0;
let failedProjects = 0;
const passedDirs = [];
const failedDirs = [];

console.log(`${colors.blue}🚀 Cryptocurrency Transaction Monitor Test Runner${colors.reset}`);
console.log(`${colors.blue}=================================================${colors.reset}`);
console.log('');

/**
 * Run tests in a specific directory
 */
function runTestsInDir(dir) {
    const projectName = path.basename(dir);
    
    console.log(`${colors.yellow}📁 Testing: ${projectName}${colors.reset}`);
    console.log(`   Directory: ${dir}`);
    
    // Check if directory exists
    if (!fs.existsSync(dir)) {
        console.log(`   ${colors.red}❌ Directory not found${colors.reset}`);
        return false;
    }
    
    // Check if package.json exists
    const packageJsonPath = path.join(dir, 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
        console.log(`   ${colors.red}❌ No package.json found${colors.reset}`);
        return false;
    }
    
    // Check if test script exists
    try {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        if (!packageJson.scripts || !packageJson.scripts.test) {
            console.log(`   ${colors.red}❌ No test script defined in package.json${colors.reset}`);
            return false;
        }
    } catch (error) {
        console.log(`   ${colors.red}❌ Invalid package.json${colors.reset}`);
        return false;
    }
    
    try {
        // Install dependencies
        console.log('   Installing dependencies...');
        execSync('npm install', { 
            cwd: dir, 
            stdio: 'pipe'
        });
        console.log(`   ${colors.green}✅ Dependencies installed${colors.reset}`);
        
        // Run tests
        console.log('   Running tests...');
        const testOutput = execSync('npm test', { 
            cwd: dir, 
            stdio: 'pipe',
            encoding: 'utf8'
        });
        
        console.log(`   ${colors.green}✅ Tests PASSED${colors.reset}`);
        
        // Show test summary
        const lines = testOutput.split('\n');
        const summaryLines = lines.filter(line => 
            line.includes('passing') || 
            line.includes('passed') || 
            line.includes('✓') ||
            line.includes('PASS')
        ).slice(0, 3);
        
        summaryLines.forEach(line => {
            console.log(`   ${line.trim()}`);
        });
        
        return true;
        
    } catch (error) {
        console.log(`   ${colors.red}❌ Tests FAILED${colors.reset}`);
        console.log('   Error details:');
        
        const errorOutput = error.stdout || error.stderr || error.message;
        const errorLines = errorOutput.toString().split('\n').slice(-10);
        errorLines.forEach(line => {
            if (line.trim()) {
                console.log(`   ${line}`);
            }
        });
        
        return false;
    }
}

/**
 * Find all cryptocurrency monitor directories
 */
function findCryptoDirectories() {
    const cryptoPatterns = ['bch', 'btc', 'nem', 'eth', 'ltc'];
    const cryptoDirs = [];
    
    try {
        const items = fs.readdirSync('.', { withFileTypes: true });
        
        for (const item of items) {
            if (item.isDirectory()) {
                const dirName = item.name.toLowerCase();
                if (cryptoPatterns.some(pattern => dirName.startsWith(pattern))) {
                    cryptoDirs.push(item.name);
                }
            }
        }
    } catch (error) {
        console.error(`${colors.red}Error reading directory: ${error.message}${colors.reset}`);
        process.exit(1);
    }
    
    return cryptoDirs;
}

// Main execution
console.log('🔍 Scanning for cryptocurrency monitor directories...');
const cryptoDirs = findCryptoDirectories();

if (cryptoDirs.length === 0) {
    console.log(`${colors.red}❌ No cryptocurrency monitor directories found${colors.reset}`);
    console.log('   Looking for directories starting with: bch*, btc*, nem*, eth*, ltc*');
    process.exit(1);
}

console.log(`${colors.green}Found ${cryptoDirs.length} cryptocurrency monitor directories:${colors.reset}`);
cryptoDirs.forEach(dir => {
    console.log(`   - ${dir}`);
});
console.log('');

// Run tests for each directory
for (const dir of cryptoDirs) {
    totalProjects++;
    
    if (runTestsInDir(dir)) {
        passedProjects++;
        passedDirs.push(dir);
    } else {
        failedProjects++;
        failedDirs.push(dir);
    }
    
    console.log('');
}

// Summary
console.log(`${colors.blue}📊 TEST SUMMARY${colors.reset}`);
console.log(`${colors.blue}===============${colors.reset}`);
console.log(`Total Projects: ${totalProjects}`);
console.log(`${colors.green}Passed: ${passedProjects}${colors.reset}`);
console.log(`${colors.red}Failed: ${failedProjects}${colors.reset}`);
console.log('');

if (passedDirs.length > 0) {
    console.log(`${colors.green}✅ PASSED PROJECTS:${colors.reset}`);
    passedDirs.forEach(dir => {
        console.log(`   ${colors.green}✓${colors.reset} ${dir}`);
    });
    console.log('');
}

if (failedDirs.length > 0) {
    console.log(`${colors.red}❌ FAILED PROJECTS:${colors.reset}`);
    failedDirs.forEach(dir => {
        console.log(`   ${colors.red}✗${colors.reset} ${dir}`);
    });
    console.log('');
}

// Exit with appropriate code
if (failedProjects === 0) {
    console.log(`${colors.green}🎉 All tests passed successfully!${colors.reset}`);
    process.exit(0);
} else {
    console.log(`${colors.red}💥 Some tests failed. Check the output above for details.${colors.reset}`);
    process.exit(1);
}
