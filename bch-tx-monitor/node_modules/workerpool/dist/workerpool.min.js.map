{"version": 3, "file": "workerpool.min.js", "mappings": ";CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,aAAc,GAAIH,GACC,iBAAZC,QACdA,QAAoB,WAAID,IAExBD,EAAiB,WAAIC,GACtB,CATD,CASoB,oBAATK,KAAuBA,KAAOC,MAAO,WAChD,+DCVA,IAAIC,EAAUC,EAAQ,KAClBC,EAAgBD,EAAQ,KACxBE,EAAcF,EAAQ,KAEtBG,EAAuB,IADFH,EAAQ,MAQjC,SAASI,EAAKC,EAAQC,GACE,iBAAXD,EACTP,KAAKO,OAASA,GAAU,MAGxBP,KAAKO,OAAS,KACdC,EAAUD,GAGZP,KAAKS,QAAU,GACfT,KAAKU,MAAQ,GAEbF,EAAUA,GAAW,CAAC,EAEtBR,KAAKW,SAAWC,OAAOC,OAAOL,EAAQG,UAAY,IAClDX,KAAKc,SAAWF,OAAOC,OAAOL,EAAQM,UAAY,CAAC,GACnDd,KAAKe,WAAaH,OAAOC,OAAOL,EAAQO,YAAc,CAAC,GACvDf,KAAKgB,iBAAmBJ,OAAOC,OAAOL,EAAQQ,kBAAoB,CAAC,GACnEhB,KAAKiB,eAAkBT,EAAQS,gBAAkB,MACjDjB,KAAKkB,WAAaV,EAAQU,WAC1BlB,KAAKmB,WAAaX,EAAQW,YAAcX,EAAQU,YAAc,OAC9DlB,KAAKoB,aAAeZ,EAAQY,cAAgBC,IAC5CrB,KAAKsB,uBAAyBd,EAAQc,wBAA0B,IAEhEtB,KAAKuB,eAAiBf,EAAQe,gBAAmB,kBAAM,IAAI,EAC3DvB,KAAKwB,kBAAoBhB,EAAQgB,mBAAsB,kBAAM,IAAI,EAG7DhB,GAAW,eAAgBA,GAyXjC,SAA4BiB,GAC1B,IAAKC,EAASD,KAAgBE,EAAUF,IAAeA,EAAa,EAClE,MAAM,IAAIG,UAAU,mDAExB,CA5XIC,CAAmBrB,EAAQiB,YAC3BzB,KAAKyB,WAAajB,EAAQiB,YAG1BzB,KAAKyB,WAAaK,KAAKC,KAAK3B,EAAY4B,MAAQ,GAAK,EAAG,GAGtDxB,GAAW,eAAgBA,IACH,QAAvBA,EAAQyB,WACTjC,KAAKiC,WAAajC,KAAKyB,YA0X7B,SAA4BQ,GAC1B,IAAKP,EAASO,KAAgBN,EAAUM,IAAeA,EAAa,EAClE,MAAM,IAAIL,UAAU,mDAExB,CA5XMM,CAAmB1B,EAAQyB,YAC3BjC,KAAKiC,WAAazB,EAAQyB,WAC1BjC,KAAKyB,WAAaK,KAAKC,IAAI/B,KAAKiC,WAAYjC,KAAKyB,aAEnDzB,KAAKmC,qBAGPnC,KAAKoC,WAAapC,KAAKqC,MAAMC,KAAKtC,MAGV,WAApBA,KAAKmB,YACPhB,EAAcoC,qBAElB,CAsXA,SAASb,EAASc,GAChB,MAAwB,iBAAVA,CAChB,CAOA,SAASb,EAAUa,GACjB,OAAOV,KAAKW,MAAMD,IAAUA,CAC9B,CA9VAlC,EAAKoC,UAAUC,KAAO,SAAUC,EAAQC,EAAQrC,GAE9C,GAAIqC,IAAWC,MAAMC,QAAQF,GAC3B,MAAM,IAAIjB,UAAU,uCAGtB,GAAsB,iBAAXgB,EAAqB,CAC9B,IAAII,EAAW/C,EAAQgD,QAEvB,GAAIjD,KAAKU,MAAMwC,QAAUlD,KAAKoB,aAC5B,MAAM,IAAI+B,MAAM,qBAAuBnD,KAAKoB,aAAe,YAI7D,IAAIV,EAAQV,KAAKU,MACb0C,EAAO,CACTR,OAASA,EACTC,OAASA,EACTG,SAAUA,EACVK,QAAS,KACT7C,QAASA,GAEXE,EAAM4C,KAAKF,GAIX,IAAIG,EAAkBP,EAASQ,QAAQH,QAgBvC,OAfAL,EAASQ,QAAQH,QAAU,SAAkBI,GAC3C,OAA6B,IAAzB/C,EAAMgD,QAAQN,IAEhBA,EAAKC,QAAUI,EACRT,EAASQ,SAITD,EAAgBI,KAAKX,EAASQ,QAASC,EAElD,EAGAzD,KAAKqC,QAEEW,EAASQ,OAClB,CACK,GAAsB,mBAAXZ,EAEd,OAAO5C,KAAK2C,KAAK,MAAO,CAACiB,OAAOhB,GAASC,IAGzC,MAAM,IAAIjB,UAAU,mDAExB,EAQAtB,EAAKoC,UAAUmB,MAAQ,WACrB,GAAIC,UAAUZ,OAAS,EACrB,MAAM,IAAIC,MAAM,yBAGlB,IAAIY,EAAO/D,KACX,OAAOA,KAAK2C,KAAK,WACZqB,MAAK,SAAUC,GACd,IAAIJ,EAAQ,CAAC,EAQb,OANAI,EAAQC,SAAQ,SAAUtB,GACxBiB,EAAMjB,GAAU,WACd,OAAOmB,EAAKpB,KAAKC,EAAQE,MAAMJ,UAAUyB,MAAMR,KAAKG,WACtD,CACF,IAEOD,CACT,GACN,EAsBAvD,EAAKoC,UAAUL,MAAQ,WACrB,GAAIrC,KAAKU,MAAMwC,OAAS,EAAG,CAIzB,IAAIkB,EAASpE,KAAKqE,aAClB,GAAID,EAAQ,CAEV,IAAIE,EAAKtE,KACLoD,EAAOpD,KAAKU,MAAM6D,QAGtB,GAAInB,EAAKJ,SAASQ,QAAQgB,QAAS,CAEjC,IAAIhB,EAAUY,EAAOzB,KAAKS,EAAKR,OAAQQ,EAAKP,OAAQO,EAAKJ,SAAUI,EAAK5C,SACrEwD,KAAKM,EAAGlC,YAAW,OACb,WAEL,GAAIgC,EAAOK,WACT,OAAOH,EAAGI,cAAcN,EAE5B,IAAGJ,MAAK,WACNM,EAAGjC,OACL,IAG0B,iBAAjBe,EAAKC,SACdG,EAAQH,QAAQD,EAAKC,QAEzB,MAEEiB,EAAGjC,OAEP,CACF,CACF,EAWA/B,EAAKoC,UAAU2B,WAAa,WAG1B,IADA,IAAI5D,EAAUT,KAAKS,QACVkE,EAAI,EAAGA,EAAIlE,EAAQyC,OAAQyB,IAAK,CACvC,IAAIP,EAAS3D,EAAQkE,GACrB,IAAsB,IAAlBP,EAAOQ,OACT,OAAOR,CAEX,CAEA,OAAI3D,EAAQyC,OAASlD,KAAKyB,YAExB2C,EAASpE,KAAK6E,uBACdpE,EAAQ6C,KAAKc,GACNA,GAGF,IACT,EAUA9D,EAAKoC,UAAUgC,cAAgB,SAASN,GACtC,IAAIE,EAAKtE,KAQT,OANAK,EAAqByE,YAAYV,EAAOW,WAExC/E,KAAKgF,sBAAsBZ,GAE3BpE,KAAKmC,oBAEE,IAAIlC,GAAQ,SAASgF,EAASC,GACnCd,EAAOe,WAAU,GAAO,SAASC,GAC/Bd,EAAG9C,kBAAkB,CACnBb,SAAUyD,EAAOzD,SACjBG,SAAUsD,EAAOtD,SACjBE,iBAAkBoD,EAAOpD,iBACzBT,OAAQ6D,EAAO7D,SAEb6E,EACFF,EAAOE,GAEPH,EAAQb,EAEZ,GACF,GACF,EAOA9D,EAAKoC,UAAUsC,sBAAwB,SAASZ,GAE9C,IAAIiB,EAAQrF,KAAKS,QAAQiD,QAAQU,IAClB,IAAXiB,GACFrF,KAAKS,QAAQ6E,OAAOD,EAAO,EAE/B,EAYA/E,EAAKoC,UAAUyC,UAAY,SAAUI,EAAOlC,GAC1C,IAAIiB,EAAKtE,KAGTA,KAAKU,MAAMwD,SAAQ,SAAUd,GAC3BA,EAAKJ,SAASkC,OAAO,IAAI/B,MAAM,mBACjC,IACAnD,KAAKU,MAAMwC,OAAS,EAEpB,IAIIsC,EAJI,SAAUpB,GAChB/D,EAAqByE,YAAYV,EAAOW,WACxC/E,KAAKgF,sBAAsBZ,EAC7B,EACqB9B,KAAKtC,MAEtByF,EAAW,GAef,OAdczF,KAAKS,QAAQ0D,QACnBD,SAAQ,SAAUE,GACxB,IAAIsB,EAActB,EAAOuB,mBAAmBJ,EAAOlC,GAChDW,KAAKwB,GACLI,QAAO,WACNtB,EAAG9C,kBAAkB,CACnBb,SAAUyD,EAAOzD,SACjBG,SAAUsD,EAAOtD,SACjBE,iBAAkBoD,EAAOpD,iBACzBT,OAAQ6D,EAAO7D,QAEnB,IACFkF,EAASnC,KAAKoC,EAChB,IACOzF,EAAQ4F,IAAIJ,EACrB,EAMAnF,EAAKoC,UAAUoD,MAAQ,WACrB,IAAIC,EAAe/F,KAAKS,QAAQyC,OAC5B8C,EAAchG,KAAKS,QAAQwF,QAAO,SAAU7B,GAC9C,OAAOA,EAAOQ,MAChB,IAAG1B,OAEH,MAAO,CACL6C,aAAeA,EACfC,YAAeA,EACfE,YAAeH,EAAeC,EAE9BG,aAAenG,KAAKU,MAAMwC,OAC1BkD,YAAeJ,EAEnB,EAMA1F,EAAKoC,UAAUP,kBAAoB,WACjC,GAAInC,KAAKiC,WACP,IAAI,IAAI0C,EAAI3E,KAAKS,QAAQyC,OAAQyB,EAAI3E,KAAKiC,WAAY0C,IACpD3E,KAAKS,QAAQ6C,KAAKtD,KAAK6E,uBAG7B,EAOAvE,EAAKoC,UAAUmC,qBAAuB,WACpC,IAAMwB,EAAmBrG,KAAKuB,eAAe,CAC3CZ,SAAUX,KAAKW,SACfG,SAAUd,KAAKc,SACfC,WAAYf,KAAKe,WACjBC,iBAAkBhB,KAAKgB,iBACvBT,OAAQP,KAAKO,UACT,CAAC,EAEP,OAAO,IAAIJ,EAAckG,EAAiB9F,QAAUP,KAAKO,OAAQ,CAC/DI,SAAU0F,EAAiB1F,UAAYX,KAAKW,SAC5CG,SAAUuF,EAAiBvF,UAAYd,KAAKc,SAC5CC,WAAYsF,EAAiBtF,YAAcf,KAAKe,WAChDC,iBAAkBqF,EAAiBrF,kBAAoBhB,KAAKgB,iBAC5D+D,UAAW1E,EAAqBiG,wBAAwBtG,KAAKiB,gBAC7DE,WAAYnB,KAAKmB,WACjBG,uBAAwBtB,KAAKsB,wBAEjC,EA0CA1B,EAAOD,QAAUW,gCCzbjB,SAASL,EAAQsG,EAASC,GACxB,IAAIlC,EAAKtE,KAET,KAAMA,gBAAgBC,GACpB,MAAM,IAAIwG,YAAY,oDAGxB,GAAuB,mBAAZF,EACT,MAAM,IAAIE,YAAY,uDAGxB,IAAIC,EAAa,GACbC,EAAU,GAGd3G,KAAK4G,UAAW,EAChB5G,KAAK6G,UAAW,EAChB7G,KAAKwE,SAAU,EASf,IAAIsC,EAAW,SAAUC,EAAWC,GAClCN,EAAWpD,KAAKyD,GAChBJ,EAAQrD,KAAK0D,EACf,EAQAhH,KAAKgE,KAAO,SAAU+C,EAAWC,GAC/B,OAAO,IAAI/G,GAAQ,SAAUgF,EAASC,GACpC,IAAI+B,EAAIF,EAAYG,EAAMH,EAAW9B,EAASC,GAAUD,EACpDkC,EAAIH,EAAYE,EAAMF,EAAW/B,EAASC,GAAUA,EAExD4B,EAASG,EAAGE,EACd,GAAG7C,EACL,EAOA,IAAI8C,EAAW,SAAUC,GAgBvB,OAdA/C,EAAGsC,UAAW,EACdtC,EAAGuC,UAAW,EACdvC,EAAGE,SAAU,EAEbkC,EAAWxC,SAAQ,SAAUoD,GAC3BA,EAAGD,EACL,IAEAP,EAAW,SAAUC,EAAWC,GAC9BD,EAAUM,EACZ,EAEAD,EAAWG,EAAU,WAAc,EAE5BjD,CACT,EAOIiD,EAAU,SAAUC,GAgBtB,OAdAlD,EAAGsC,UAAW,EACdtC,EAAGuC,UAAW,EACdvC,EAAGE,SAAU,EAEbmC,EAAQzC,SAAQ,SAAUoD,GACxBA,EAAGE,EACL,IAEAV,EAAW,SAAUC,EAAWC,GAC9BA,EAAOQ,EACT,EAEAJ,EAAWG,EAAU,WAAc,EAE5BjD,CACT,EAMAtE,KAAKyH,OAAS,WAQZ,OAPIjB,EACFA,EAAOiB,SAGPF,EAAQ,IAAIG,GAGPpD,CACT,EASAtE,KAAKqD,QAAU,SAAUI,GACvB,GAAI+C,EACFA,EAAOnD,QAAQI,OAEZ,CACH,IAAIkE,EAAQC,YAAW,WACrBL,EAAQ,IAAIM,EAAa,2BAA6BpE,EAAQ,OAChE,GAAGA,GAEHa,EAAGsB,QAAO,WACRkC,aAAaH,EACf,GACF,CAEA,OAAOrD,CACT,EAGAiC,GAAQ,SAAUc,GAChBD,EAASC,EACX,IAAG,SAAUG,GACXD,EAAQC,EACV,GACF,CAUA,SAASN,EAAMa,EAAU9C,EAASC,GAChC,OAAO,SAAUmC,GACf,IACE,IAAIW,EAAMD,EAASV,GACfW,GAA2B,mBAAbA,EAAIhE,MAA+C,mBAAjBgE,EAAW,MAE7DA,EAAIhE,KAAKiB,EAASC,GAGlBD,EAAQ+C,EAEZ,CACA,MAAOR,GACLtC,EAAOsC,EACT,CACF,CACF,CA0EA,SAASE,EAAkBO,GACzBjI,KAAKiI,QAAUA,GAAW,oBAC1BjI,KAAKkI,OAAS,IAAI/E,OAAS+E,KAC7B,CAcA,SAASL,EAAaI,GACpBjI,KAAKiI,QAAUA,GAAW,mBAC1BjI,KAAKkI,OAAS,IAAI/E,OAAS+E,KAC7B,CAvFAjI,EAAQyC,UAAiB,MAAI,SAAUsE,GACrC,OAAOhH,KAAKgE,KAAK,KAAMgD,EACzB,EAUA/G,EAAQyC,UAAUkD,OAAS,SAAU0B,GACnC,OAAOtH,KAAKgE,KAAKsD,EAAIA,EACvB,EAQArH,EAAQ4F,IAAM,SAAUJ,GACtB,OAAO,IAAIxF,GAAQ,SAAUgF,EAASC,GACpC,IAAIiD,EAAY1C,EAASvC,OACrBkF,EAAU,GAEVD,EACF1C,EAASvB,SAAQ,SAAUmE,EAAG1D,GAC5B0D,EAAErE,MAAK,SAAUqD,GACfe,EAAQzD,GAAK0C,EAEI,KADjBc,GAEElD,EAAQmD,EAEZ,IAAG,SAAUZ,GACXW,EAAY,EACZjD,EAAOsC,EACT,GACF,IAGAvC,EAAQmD,EAEZ,GACF,EAMAnI,EAAQgD,MAAQ,WACd,IAAID,EAAW,CAAC,EAOhB,OALAA,EAASQ,QAAU,IAAIvD,GAAQ,SAAUgF,EAASC,GAChDlC,EAASiC,QAAUA,EACnBjC,EAASkC,OAASA,CACpB,IAEOlC,CACT,EAYA0E,EAAkBhF,UAAY,IAAIS,MAClCuE,EAAkBhF,UAAU4F,YAAcnF,MAC1CuE,EAAkBhF,UAAU6F,KAAO,oBAEnCtI,EAAQyH,kBAAoBA,EAa5BG,EAAanF,UAAY,IAAIS,MAC7B0E,EAAanF,UAAU4F,YAAcnF,MACrC0E,EAAanF,UAAU6F,KAAO,eAE9BtI,EAAQ4H,aAAeA,EAGvBjI,EAAOD,QAAUM,oCCtRJ,SAAAuI,EAAAC,EAAAC,IAAA,MAAAA,GAAAA,EAAAD,EAAAvF,UAAAwF,EAAAD,EAAAvF,QAAA,QAAAyB,EAAA,EAAAgE,EAAA,IAAA7F,MAAA4F,GAAA/D,EAAA+D,EAAA/D,IAAAgE,EAAAhE,GAAA8D,EAAA9D,GAAA,OAAAgE,CAAA,UAAAC,EAAAC,EAAAC,GAAA,IAAAC,EAAAnI,OAAAoI,KAAAH,GAAA,GAAAjI,OAAAqI,sBAAA,KAAAC,EAAAtI,OAAAqI,sBAAAJ,GAAAC,IAAAI,EAAAA,EAAAjD,QAAA,SAAA6C,GAAA,OAAAlI,OAAAuI,yBAAAN,EAAAC,GAAAM,UAAA,KAAAL,EAAAzF,KAAA+F,MAAAN,EAAAG,EAAA,QAAAH,CAAA,UAAAO,EAAAJ,GAAA,OAAAI,EAAA,mBAAAC,QAAA,iBAAAA,OAAAC,SAAA,SAAAN,GAAA,cAAAA,CAAA,WAAAA,GAAA,OAAAA,GAAA,mBAAAK,QAAAL,EAAAZ,cAAAiB,QAAAL,IAAAK,OAAA7G,UAAA,gBAAAwG,CAAA,EAAAI,EAAAJ,EAAA,CAEb,IAAIjJ,EAAUC,EAAQ,KAClBE,EAAcF,EAAQ,KACtBuJ,EAAqBvJ,EAAQ,KAM7BwJ,EAAsB,2BAE1B,SAASnH,IACP,IAAIoH,EAAgBC,IACpB,IAAKD,EACH,MAAM,IAAIxG,MAAM,+EAGlB,OAAOwG,CACT,CAGA,SAASE,IAEP,GAAsB,mBAAXC,SAA4C,YAAL,oBAANA,OAAM,YAAAR,EAANQ,UAA+D,mBAAjCA,OAAOpH,UAAU4F,aACzF,MAAM,IAAInF,MAAM,wCAEpB,CAEA,SAASyG,IACP,IACE,OAAOH,EAAmB,iBAC5B,CAAE,MAAMjC,GACN,GAAqB,WAAjB8B,EAAO9B,IAAgC,OAAVA,GAAiC,qBAAfA,EAAMuC,KAEvD,OAAO,KAEP,MAAMvC,CAEV,CACF,CAgDA,SAASwC,EAAmBzJ,EAAQQ,EAAY+I,GAE9C,IAAI1F,EAAS,IAAI0F,EAAOvJ,EAAQQ,GAYhC,OAVAqD,EAAO6F,iBAAkB,EAEzB7F,EAAO8F,GAAK,SAAUC,EAAOpC,GAC3B/H,KAAKoK,iBAAiBD,GAAO,SAAUlC,GACrCF,EAASE,EAAQoC,KACnB,GACF,EACAjG,EAAOkG,KAAO,SAAUrC,EAASsC,GAC/BvK,KAAKwK,YAAYvC,EAASsC,EAC5B,EACOnG,CACT,CAEA,SAASqG,EAAwBlK,EAAQoJ,EAAee,GACtD,IAAItG,EAAS,IAAIuF,EAAcG,OAAOvJ,EA1G3B,SAAAsI,GAAA,QAAAC,EAAA,EAAAA,EAAAhF,UAAAZ,OAAA4F,IAAA,KAAAC,EAAA,MAAAjF,UAAAgF,GAAAhF,UAAAgF,GAAA,GAAAA,EAAA,EAAAF,EAAAhI,OAAAmI,IAAA,GAAA7E,SAAA,SAAA4E,GAAA,IAAA6B,EAAAC,EAAApI,EAAAmI,EAAA9B,EAAA+B,EAAA9B,EAAAtG,EAAAuG,EAAAD,IAAA8B,EAAA,SAAAC,GAAA,IAAAD,EAAA,SAAAE,EAAAC,GAAA,cAAAzB,EAAAwB,IAAA,OAAAA,EAAA,OAAAA,EAAA,IAAAE,EAAAF,EAAAvB,OAAA0B,aAAA,QAAAC,IAAAF,EAAA,KAAAhD,EAAAgD,EAAArH,KAAAmH,EAAAC,UAAA,cAAAzB,EAAAtB,GAAA,OAAAA,EAAA,UAAApG,UAAA,uDAAAgC,OAAAkH,EAAA,CAAAK,CAAAN,GAAA,iBAAAvB,EAAAsB,GAAAA,EAAAhH,OAAAgH,EAAA,CAAAQ,CAAAR,MAAAD,EAAA/J,OAAAyK,eAAAV,EAAAC,EAAA,CAAApI,MAAAA,EAAA4G,YAAA,EAAAkC,cAAA,EAAAC,UAAA,IAAAZ,EAAAC,GAAApI,CAAA,IAAA5B,OAAA4K,0BAAA5K,OAAA6K,iBAAA5C,EAAAjI,OAAA4K,0BAAAzC,IAAAH,EAAAhI,OAAAmI,IAAA7E,SAAA,SAAA4E,GAAAlI,OAAAyK,eAAAxC,EAAAC,EAAAlI,OAAAuI,yBAAAJ,EAAAD,GAAA,WAAAD,CAAA,CA0GiC6C,CAAA,CAC1CC,QAAQ,EACRC,QAAQ,GACLlB,IAgBL,OAdAtG,EAAOyH,gBAAiB,EACxBzH,EAAOkG,KAAO,SAASrC,EAASsC,GAC9BvK,KAAKwK,YAAYvC,EAASsC,EAC5B,EAEAnG,EAAO0H,KAAO,WAEZ,OADA9L,KAAKmF,aACE,CACT,EAEAf,EAAO2H,WAAa,WAClB/L,KAAKmF,WACP,EAEOf,CACT,CAEA,SAAS4H,EAAmBzL,EAAQC,EAASyL,GAE3C,IAAI7H,EAAS6H,EAAcC,KACzB3L,EACAC,EAAQG,SACRH,EAAQM,UAINwJ,EAAOlG,EAAOkG,KAMlB,OALAlG,EAAOkG,KAAO,SAAUrC,GACtB,OAAOqC,EAAK3G,KAAKS,EAAQ6D,EAC3B,EAEA7D,EAAO+H,gBAAiB,EACjB/H,CACT,CAGA,SAASgI,EAAmBC,GAC1BA,EAAOA,GAAQ,CAAC,EAEhB,IAAIC,EAAkBC,QAAQC,SAASC,KAAK,KACxCC,GAA4D,IAA1CJ,EAAgB5I,QAAQ,aAC1CiJ,GAAuD,IAA5CL,EAAgB5I,QAAQ,eAEnC8I,EAAW,GAef,OAdIE,IACFF,EAASlJ,KAAK,aAAe+I,EAAKtH,WAE9B4H,GACFH,EAASlJ,KAAK,gBAIlBiJ,QAAQC,SAAStI,SAAQ,SAAS2G,GAC5BA,EAAInH,QAAQ,yBAA2B,GACzC8I,EAASlJ,KAAKuH,EAElB,IAEOjK,OAAOgM,OAAO,CAAC,EAAGP,EAAM,CAC7B1L,SAAU0L,EAAK1L,SACfG,SAAUF,OAAOgM,OAAO,CAAC,EAAGP,EAAKvL,SAAU,CACzC0L,UAAWH,EAAKvL,UAAYuL,EAAKvL,SAAS0L,UAAY,IACrDK,OAAOL,MAGd,CA0BA,SAASrM,EAAcI,EAAQuM,GAC7B,IAAIxI,EAAKtE,KACLQ,EAAUsM,GAAY,CAAC,EAyD3B,SAASC,EAAQvF,GAGf,IAAK,IAAIwF,KAFT1I,EAAGG,YAAa,EAEDH,EAAG2I,gBACU/B,IAAtB5G,EAAG2I,WAAWD,IAChB1I,EAAG2I,WAAWD,GAAIhK,SAASkC,OAAOsC,GAGtClD,EAAG2I,WAAarM,OAAOsM,OAAO,KAChC,CAhEAlN,KAAKO,OAASA,GAnKhB,WACE,GAA6B,YAAzBH,EAAY+M,SAAwB,CAEtC,GAAoB,oBAATC,KACT,MAAM,IAAIjK,MAAM,qCAElB,IAAKkK,OAAOC,KAA6C,mBAA/BD,OAAOC,IAAIC,gBACnC,MAAM,IAAIpK,MAAM,oDAIlB,IAAIqK,EAAO,IAAIJ,KAAK,CAAClN,EAAQ,MAAgC,CAACuN,KAAM,oBACpE,OAAOJ,OAAOC,IAAIC,gBAAgBC,EACpC,CAGE,OAAOE,UAAY,YAEvB,CAiJ0BC,GACxB3N,KAAKoE,OAhJP,SAAqB7D,EAAQC,GAC3B,GAA2B,QAAvBA,EAAQW,WAEV,OADA0I,IACOG,EAAmBzJ,EAAQC,EAAQO,WAAY+I,QACjD,GAA2B,WAAvBtJ,EAAQW,WAEjB,OAAOsJ,EAAwBlK,EAD/BoJ,EAAgBpH,IACsC/B,EAAQQ,kBACzD,GAA2B,YAAvBR,EAAQW,YAA6BX,EAAQW,WAEjD,CACL,GAA6B,YAAzBf,EAAY+M,SAEd,OADAtD,IACOG,EAAmBzJ,EAAQC,EAAQO,WAAY+I,QAGtD,IAAIH,EAAgBC,IACpB,OAAID,EACKc,EAAwBlK,EAAQoJ,EAAenJ,EAAQQ,kBAEvDgL,EAAmBzL,EAAQ6L,EAAmB5L,GAAUiJ,EAAmB,iBAGxF,CAdE,OAAOuC,EAAmBzL,EAAQ6L,EAAmB5L,GAAUiJ,EAAmB,iBAetF,CAyHgBmE,CAAY5N,KAAKO,OAAQC,GACvCR,KAAK+E,UAAYvE,EAAQuE,UACzB/E,KAAKc,SAAWN,EAAQM,SACxBd,KAAKW,SAAWH,EAAQG,SACxBX,KAAKe,WAAaP,EAAQO,WAC1Bf,KAAKgB,iBAAmBR,EAAQQ,iBAChChB,KAAKsB,uBAAyBd,EAAQc,uBAGjCf,IACHP,KAAKoE,OAAOyJ,OAAQ,GAItB7N,KAAK8N,aAAe,GACpB9N,KAAKoE,OAAO8F,GAAG,WAAW,SAAU6D,GAClC,IAAIzJ,EAAGG,WAGP,GAAwB,iBAAbsJ,GAAsC,UAAbA,EAClCzJ,EAAGF,OAAOyJ,OAAQ,EA8CtB,WACA,IACgDG,EADhDC,EAlRW,SAAA/E,EAAAgF,GAAA,IAAAC,EAAA,oBAAA5E,QAAAL,EAAAK,OAAAC,WAAAN,EAAA,kBAAAiF,EAAA,IAAArL,MAAAC,QAAAmG,KAAAiF,EAAA,SAAAjF,EAAAkF,GAAA,GAAAlF,EAAA,qBAAAA,EAAA,OAAAV,EAAAU,EAAAkF,GAAA,IAAAC,EAAAzN,OAAA8B,UAAA4L,SAAA3K,KAAAuF,GAAA/E,MAAA,uBAAAkK,GAAAnF,EAAAZ,cAAA+F,EAAAnF,EAAAZ,YAAAC,MAAA,QAAA8F,GAAA,QAAAA,EAAAvL,MAAAyL,KAAArF,GAAA,cAAAmF,GAAA,2CAAAG,KAAAH,GAAA7F,EAAAU,EAAAkF,QAAA,GAAAK,CAAAvF,KAAAgF,GAAAhF,GAAA,iBAAAA,EAAAhG,OAAA,CAAAiL,IAAAjF,EAAAiF,GAAA,IAAAxJ,EAAA,EAAA+J,EAAA,oBAAAzH,EAAAyH,EAAAL,EAAA,kBAAA1J,GAAAuE,EAAAhG,OAAA,CAAAyL,MAAA,IAAAA,MAAA,EAAAnM,MAAA0G,EAAAvE,KAAA,EAAAkE,EAAA,SAAA+F,GAAA,MAAAA,CAAA,EAAAzH,EAAAuH,EAAA,WAAA9M,UAAA,6IAAAwD,EAAAyJ,GAAA,EAAAC,GAAA,SAAA7H,EAAA,WAAAkH,EAAAA,EAAAxK,KAAAuF,EAAA,EAAAmF,EAAA,eAAAU,EAAAZ,EAAAa,OAAA,OAAAH,EAAAE,EAAAJ,KAAAI,CAAA,EAAAlG,EAAA,SAAAoG,GAAAH,GAAA,EAAA1J,EAAA6J,CAAA,EAAA9H,EAAA,eAAA0H,GAAA,MAAAV,EAAA,QAAAA,EAAA,oBAAAW,EAAA,MAAA1J,CAAA,IAkRX8J,CACuB5K,EAAGwJ,aAAaxI,OAAO,IAAE,IAA9C,IAAA2I,EAAAhH,MAAA+G,EAAAC,EAAAI,KAAAM,MAAgD,KAAtCQ,EAAOnB,EAAAxL,MACf8B,EAAGF,OAAOkG,KAAK6E,EAAQlH,QAASkH,EAAQ5E,SAC1C,CAAC,OAAAnF,GAAA6I,EAAApF,EAAAzD,EAAA,SAAA6I,EAAA9G,GAAA,CACH,CAlDIiI,OACK,CAEL,IAAIpC,EAAKe,EAASf,GACd5J,EAAOkB,EAAG2I,WAAWD,QACZ9B,IAAT9H,IACE2K,EAASsB,QACPjM,EAAK5C,SAAsC,mBAApB4C,EAAK5C,QAAQ0J,IACtC9G,EAAK5C,QAAQ0J,GAAG6D,EAASuB,iBAIpBhL,EAAG2I,WAAWD,IAGE,IAAnB1I,EAAGiL,aAELjL,EAAGa,YAID4I,EAASvG,MACXpE,EAAKJ,SAASkC,OAnE1B,SAAwByF,GAItB,IAHA,IAAI6E,EAAO,IAAIrM,MAAM,IACjBsM,EAAQ7O,OAAOoI,KAAK2B,GAEfhG,EAAI,EAAGA,EAAI8K,EAAMvM,OAAQyB,IAChC6K,EAAKC,EAAM9K,IAAMgG,EAAI8E,EAAM9K,IAG7B,OAAO6K,CACT,CA0DiCE,CAAc3B,EAASvG,QAG5CpE,EAAKJ,SAASiC,QAAQ8I,EAAS1G,SAIvC,CACF,IAsBA,IAAIjD,EAASpE,KAAKoE,OAElBpE,KAAKoE,OAAO8F,GAAG,QAAS6C,GACxB/M,KAAKoE,OAAO8F,GAAG,QAAQ,SAAUyF,EAAUC,GACzC,IAAI3H,EAAU,8CAEdA,GAAW,kBAAoB0H,EAAW,MAC1C1H,GAAW,oBAAsB2H,EAAa,MAE9C3H,GAAW,2BAA8B3D,EAAG/D,OAAS,MACrD0H,GAAW,mBAAsB7D,EAAOyL,UAAY,MACpD5H,GAAW,mBAAqB7D,EAAO0L,UAAY,MAEnD7H,GAAW,gBAAkB7D,EAAOuH,OAAS,MAC7C1D,GAAW,gBAAkB7D,EAAOwH,OAAS,MAE7CmB,EAAQ,IAAI5J,MAAM8E,GACpB,IAEAjI,KAAKiN,WAAarM,OAAOsM,OAAO,MAEhClN,KAAKuP,aAAc,EACnBvP,KAAKyE,YAAa,EAClBzE,KAAK+P,UAAW,EAChB/P,KAAKgQ,mBAAqB,KAC1BhQ,KAAKiQ,OAAS,CAChB,CAMA9P,EAAcuC,UAAUuB,QAAU,WAChC,OAAOjE,KAAK2C,KAAK,UACnB,EAUAxC,EAAcuC,UAAUC,KAAO,SAASC,EAAQC,EAAQG,EAAUxC,GAC3DwC,IACHA,EAAW/C,EAAQgD,SAIrB,IAAI+J,IAAOhN,KAAKiQ,OAGhBjQ,KAAKiN,WAAWD,GAAM,CACpBA,GAAIA,EACJhK,SAAUA,EACVxC,QAASA,GAIX,IAAI2O,EAAU,CACZlH,QAAS,CACP+E,GAAIA,EACJpK,OAAQA,EACRC,OAAQA,GAEV0H,SAAU/J,GAAWA,EAAQ+J,UAG3BvK,KAAKyE,WACPzB,EAASkC,OAAO,IAAI/B,MAAM,yBACjBnD,KAAKoE,OAAOyJ,MAErB7N,KAAKoE,OAAOkG,KAAK6E,EAAQlH,QAASkH,EAAQ5E,UAE1CvK,KAAK8N,aAAaxK,KAAK6L,GAIzB,IAAI7K,EAAKtE,KACT,OAAOgD,EAASQ,QAAO,OAAO,SAAUgE,GACtC,GAAIA,aAAiBvH,EAAQyH,mBAAqBF,aAAiBvH,EAAQ4H,aAMzE,cAHOvD,EAAG2I,WAAWD,GAGd1I,EAAGqB,oBAAmB,GAC1B3B,MAAK,WACJ,MAAMwD,CACR,IAAG,SAASpC,GACV,MAAMA,CACR,IAEF,MAAMoC,CAEV,GACF,EAMArH,EAAcuC,UAAUkC,KAAO,WAC7B,OAAO5E,KAAK+P,UAAYnP,OAAOoI,KAAKhJ,KAAKiN,YAAY/J,OAAS,CAChE,EAUA/C,EAAcuC,UAAUyC,UAAY,SAAUI,EAAOwC,GACnD,IAAIzD,EAAKtE,KACT,GAAIuF,EAAO,CAET,IAAK,IAAIyH,KAAMhN,KAAKiN,gBACU/B,IAAxBlL,KAAKiN,WAAWD,IAClBhN,KAAKiN,WAAWD,GAAIhK,SAASkC,OAAO,IAAI/B,MAAM,sBAGlDnD,KAAKiN,WAAarM,OAAOsM,OAAO,KAClC,CAKA,GAHwB,mBAAbnF,IACT/H,KAAKgQ,mBAAqBjI,GAEvB/H,KAAK4E,OA+DR5E,KAAKuP,aAAc,MA/DH,CAEhB,IAAIW,EAAU,SAAS9K,GASrB,GARAd,EAAGG,YAAa,EAChBH,EAAGyL,UAAW,EACG,MAAbzL,EAAGF,QAAkBE,EAAGF,OAAO+L,oBAEjC7L,EAAGF,OAAO+L,mBAAmB,WAE/B7L,EAAGF,OAAS,KACZE,EAAGiL,aAAc,EACbjL,EAAG0L,mBACL1L,EAAG0L,mBAAmB5K,EAAKd,QACtB,GAAIc,EACT,MAAMA,CAEV,EAEA,GAAIpF,KAAKoE,OAAQ,CACf,GAAgC,mBAArBpE,KAAKoE,OAAO0H,KAAqB,CAC1C,GAAI9L,KAAKoE,OAAOgM,OAEd,YADAF,EAAQ,IAAI/M,MAAM,2BAKpB,IAAIkN,EAAmBzI,YAAW,WAC5BtD,EAAGF,QACLE,EAAGF,OAAO0H,MAEd,GAAG9L,KAAKsB,wBAmBR,OAjBAtB,KAAKoE,OAAOkM,KAAK,QAAQ,WACvBxI,aAAauI,GACT/L,EAAGF,SACLE,EAAGF,OAAOgM,QAAS,GAErBF,GACF,IAEIlQ,KAAKoE,OAAOyJ,MACd7N,KAAKoE,OAAOkG,KAAKZ,GAEjB1J,KAAK8N,aAAaxK,KAAK,CAAE2E,QAASyB,SAKpC1J,KAAK+P,UAAW,EAElB,CACK,GAAqC,mBAA1B/P,KAAKoE,OAAOe,UAK1B,MAAM,IAAIhC,MAAM,8BAJhBnD,KAAKoE,OAAOe,YACZnF,KAAKoE,OAAOgM,QAAS,CAKzB,CACAF,GACF,CAKF,EAYA/P,EAAcuC,UAAUiD,mBAAqB,SAAUJ,EAAOlC,GAC5D,IAAIL,EAAW/C,EAAQgD,QAWvB,OAVII,GACFL,EAASQ,QAAQH,QAAQA,GAE3BrD,KAAKmF,UAAUI,GAAO,SAASH,EAAKhB,GAC9BgB,EACFpC,EAASkC,OAAOE,GAEhBpC,EAASiC,QAAQb,EAErB,IACOpB,EAASQ,OAClB,EAEA5D,EAAOD,QAAUQ,EACjBP,EAAOD,QAAQ4Q,yBAA2B3G,EAC1ChK,EAAOD,QAAQ6Q,oBAAsBxE,EACrCpM,EAAOD,QAAQ8Q,oBAAsBzG,EACrCpK,EAAOD,QAAQ+Q,yBAA2BjG,EAC1C7K,EAAOD,QAAQ4C,oBAAsBA,gCCvfrC,SAASoO,IACP3Q,KAAK4Q,MAAQhQ,OAAOsM,OAAO,MAC3BlN,KAAKkD,OAAS,CAChB,CAJAtD,EAAOD,QAAUgR,EAMjBA,EAAmBjO,UAAU4D,wBAA0B,SAASuK,GAC9D,MAAgC,IAAzB7Q,KAAK4Q,MAAMC,IAChBA,IAGF,GAAIA,GAZU,MAaZ,MAAM,IAAI1N,MAAM,wCAA0C0N,EAA1C,YAKlB,OAFA7Q,KAAK4Q,MAAMC,IAAY,EACvB7Q,KAAKkD,SACE2N,CACT,EAEAF,EAAmBjO,UAAUoC,YAAc,SAASgM,UAC3C9Q,KAAK4Q,MAAME,GAClB9Q,KAAKkD,QACP,uBC1BA,IAAIuG,EAAqBvJ,EAAQ,KAG7B6Q,EAAS,SAAUC,GACrB,YACyB,IAAhBA,GACiB,MAAxBA,EAAYC,UACiB,MAA7BD,EAAYC,SAASC,IAEzB,EACAtR,EAAOD,QAAQoR,OAASA,EAGxBnR,EAAOD,QAAQwN,SAA8B,oBAAZZ,SAA2BwE,EAAOxE,SAC/D,OACA,UAIJ,IAAI4E,EAUJ,SAAgCvR,GAC9B,IACE,OAAO6J,EAZgC,iBAazC,CAAE,MAAMrE,GACN,OAAO,IACT,CACF,CAhBqBgM,GACrBxR,EAAOD,QAAQ0R,aAA2C,SAA5BzR,EAAOD,QAAQwN,WACtCgE,GAAkBA,EAAeE,gBAAkB9E,QAAQ+E,UAC5C,oBAAXC,OAGX3R,EAAOD,QAAQqC,KAAmC,YAA5BpC,EAAOD,QAAQwN,SACjCpN,KAAKyR,UAAUC,oBACfhI,EAAmB,MAAMzH,OAAOkB,wBCtBpCtD,EAAOD,QAAU,mhHCJjB,IAAI8J,mBAAqBiI,KACrB,0HAKJ9R,OAAOD,QAAU8J,oCCIjB7J,EAAOD,QALP,SAAkBsI,EAASsC,GACzBvK,KAAKiI,QAAUA,EACfjI,KAAKuK,SAAWA,CAClB,6TCLA,IAAIoH,SAAWzR,oBAAQ,KAGnBuJ,mBAAqBiI,KACrB,0HASAhI,oBAAsB,2BAMtBtF,OAAS,CACXwN,KAAM,WAAY,GAEpB,GAAoB,oBAAT7R,MAA+C,mBAAhByK,aAA0D,mBAArBJ,iBAE7EhG,OAAO8F,GAAK,SAAUC,EAAOpC,GAC3BqC,iBAAiBD,GAAO,SAAUlC,GAChCF,EAASE,EAAQoC,KACnB,GACF,EACAjG,OAAOkG,KAAO,SAAUrC,GACtBuC,YAAYvC,EACd,MAEG,IAAuB,oBAAZsE,QAmCd,MAAM,IAAIpJ,MAAM,uCAhChB,IAAIwG,cACJ,IACEA,cAAgBF,mBAAmB,iBACrC,CAAE,MAAMjC,GACN,GAAqB,WAAjB8B,QAAO9B,IAAgC,OAAVA,GAAiC,qBAAfA,EAAMuC,KAGvD,MAAMvC,CAEV,CAEA,GAAImC,eAE2B,OAA7BA,cAAckI,WAAqB,CACnC,IAAIA,WAAclI,cAAckI,WAChCzN,OAAOkG,KAAOuH,WAAWrH,YAAYlI,KAAKuP,YAC1CzN,OAAO8F,GAAK2H,WAAW3H,GAAG5H,KAAKuP,YAC/BzN,OAAOwN,KAAOrF,QAAQqF,KAAKtP,KAAKiK,QAClC,MACEnI,OAAO8F,GAAKqC,QAAQrC,GAAG5H,KAAKiK,SAE5BnI,OAAOkG,KAAO,SAAUrC,GACtBsE,QAAQjC,KAAKrC,EACf,EAEA7D,OAAO8F,GAAG,cAAc,WACtBqC,QAAQqF,KAAK,EACf,IACAxN,OAAOwN,KAAOrF,QAAQqF,KAAKtP,KAAKiK,QAKpC,CAEA,SAASuF,aAAatK,GACpB,OAAO5G,OAAOmR,oBAAoBvK,GAAOwK,QAAO,SAASC,EAAS1J,GAChE,OAAO3H,OAAOyK,eAAe4G,EAAS1J,EAAM,CAC/C/F,MAAOgF,EAAMe,GACba,YAAY,GAEX,GAAG,CAAC,EACN,CAQA,SAAS8I,UAAU1P,GACjB,OAAOA,GAAgC,mBAAfA,EAAMwB,MAAgD,mBAAhBxB,EAAK,KACrE,CAGA4B,OAAOH,QAAU,CAAC,EAQlBG,OAAOH,QAAQkO,IAAM,SAAa7K,EAAI8K,GACpC,IAAIjL,EAAI,IAAIkL,SAAS,WAAa/K,EAAK,6BACvC,OAAOH,EAAEkC,MAAMlC,EAAGiL,EACpB,EAMAhO,OAAOH,QAAQA,QAAU,WACvB,OAAOrD,OAAOoI,KAAK5E,OAAOH,QAC5B,EAKAG,OAAO4L,wBAAqB9E,EAO5B9G,OAAOkO,eAAiB,SAASvI,GAC/B,IAAIwI,EAAQ,WACVnO,OAAOwN,KAAK7H,EACd,EAEA,IAAI3F,OAAO4L,mBACT,OAAOuC,IAGT,IAAIlL,EAASjD,OAAO4L,mBAAmBjG,GACnCmI,UAAU7K,GACZA,EAAOrD,KAAKuO,EAAOA,GAEnBA,GAEJ,EAEA,IAAIC,iBAAmB,KAEvBpO,OAAO8F,GAAG,WAAW,SAAUiF,GAC7B,GAAIA,IAAYzF,oBACd,OAAOtF,OAAOkO,eAAe,GAE/B,IACE,IAAI1P,EAASwB,OAAOH,QAAQkL,EAAQvM,QAEpC,IAAIA,EAsDF,MAAM,IAAIO,MAAM,mBAAqBgM,EAAQvM,OAAS,KArDtD4P,iBAAmBrD,EAAQnC,GAG3B,IAAI3F,EAASzE,EAAOyG,MAAMzG,EAAQuM,EAAQtM,QAEtCqP,UAAU7K,GAEZA,EACKrD,MAAK,SAAUqD,GACVA,aAAkBsK,SACpBvN,OAAOkG,KAAK,CACV0C,GAAImC,EAAQnC,GACZ3F,OAAQA,EAAOY,QACfT,MAAO,MACNH,EAAOkD,UAEVnG,OAAOkG,KAAK,CACV0C,GAAImC,EAAQnC,GACZ3F,OAAQA,EACRG,MAAO,OAGXgL,iBAAmB,IACrB,IAAE,OACK,SAAUpN,GACfhB,OAAOkG,KAAK,CACV0C,GAAImC,EAAQnC,GACZ3F,OAAQ,KACRG,MAAOsK,aAAa1M,KAEtBoN,iBAAmB,IACrB,KAIAnL,aAAkBsK,SACpBvN,OAAOkG,KAAK,CACV0C,GAAImC,EAAQnC,GACZ3F,OAAQA,EAAOY,QACfT,MAAO,MACNH,EAAOkD,UAEVnG,OAAOkG,KAAK,CACV0C,GAAImC,EAAQnC,GACZ3F,OAAQA,EACRG,MAAO,OAIXgL,iBAAmB,KAMzB,CACA,MAAOpN,GACLhB,OAAOkG,KAAK,CACV0C,GAAImC,EAAQnC,GACZ3F,OAAQ,KACRG,MAAOsK,aAAa1M,IAExB,CACF,IAOAhB,OAAOqO,SAAW,SAAUxO,EAASzD,GAEnC,GAAIyD,EACF,IAAK,IAAIsE,KAAQtE,EACXA,EAAQyO,eAAenK,KACzBnE,OAAOH,QAAQsE,GAAQtE,EAAQsE,IAKjC/H,IACF4D,OAAO4L,mBAAqBxP,EAAQmS,aAGtCvO,OAAOkG,KAAK,QACd,EAEAlG,OAAOwO,KAAO,SAAUtD,GACtB,GAAIkD,iBAAkB,CACpB,GAAIlD,aAAmBqC,SAMrB,YALAvN,OAAOkG,KAAK,CACV0C,GAAIwF,iBACJnD,SAAS,EACTC,QAASA,EAAQrH,SAChBqH,EAAQ/E,UAIbnG,OAAOkG,KAAK,CACV0C,GAAIwF,iBACJnD,SAAS,EACTC,QAAAA,GAEJ,CACF,EAGE3P,QAAQkT,IAAMzO,OAAOqO,SACrB9S,QAAQiT,KAAOxO,OAAOwO,OCpQpBE,yBAA2B,CAAC,EAGhC,SAASC,oBAAoBC,GAE5B,IAAIC,EAAeH,yBAAyBE,GAC5C,QAAqB9H,IAAjB+H,EACH,OAAOA,EAAatT,QAGrB,IAAIC,EAASkT,yBAAyBE,GAAY,CAGjDrT,QAAS,CAAC,GAOX,OAHAuT,oBAAoBF,GAAUpT,EAAQA,EAAOD,QAASoT,qBAG/CnT,EAAOD,OACf,wECtBIS,EAAcF,oBAAQ,KAQ1BP,EAAQoE,KAAO,SAAcxD,EAAQC,GAGnC,OAAO,IAFIN,oBAAQ,KAEZ,CAASK,EAAQC,EAC1B,EAOAb,EAAQyE,OAAS,SAAgBH,EAASzD,GAC3BN,oBAAQ,KACd2S,IAAI5O,EAASzD,EACtB,EAMAb,EAAQwT,WAAa,SAAoB7D,GAC1BpP,oBAAQ,KACd0S,KAAKtD,EACd,EAMA3P,EAAQM,QAAU,oBAAlBN,KAMAA,EAAQgS,SAAW,oBAAnBhS,KAEAA,EAAQwN,SAAW/M,EAAY+M,SAC/BxN,EAAQ0R,aAAejR,EAAYiR,aACnC1R,EAAQqC,KAAO5B,EAAY4B,4BXrC3B", "sources": ["webpack://workerpool/webpack/universalModuleDefinition", "webpack://workerpool/./src/Pool.js", "webpack://workerpool/./src/Promise.js", "webpack://workerpool/./src/WorkerHandler.js", "webpack://workerpool/./src/debug-port-allocator.js", "webpack://workerpool/./src/environment.js", "webpack://workerpool/./src/generated/embeddedWorker.js", "webpack://workerpool/./src/requireFoolWebpack.js", "webpack://workerpool/./src/transfer.js", "webpack://workerpool/./src/worker.js", "webpack://workerpool/webpack/bootstrap", "webpack://workerpool/./src/index.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"workerpool\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"workerpool\"] = factory();\n\telse\n\t\troot[\"workerpool\"] = factory();\n})((typeof self !== 'undefined' ? self : this), function() {\nreturn ", "var Promise = require('./Promise');\nvar WorkerHandler = require('./WorkerHandler');\nvar environment = require('./environment');\nvar DebugPortAllocator = require('./debug-port-allocator');\nvar DEBUG_PORT_ALLOCATOR = new DebugPortAllocator();\n/**\n * A pool to manage workers\n * @param {String} [script]   Optional worker script\n * @param {WorkerPoolOptions} [options]  See docs\n * @constructor\n */\nfunction Pool(script, options) {\n  if (typeof script === 'string') {\n    this.script = script || null;\n  }\n  else {\n    this.script = null;\n    options = script;\n  }\n\n  this.workers = [];  // queue with all workers\n  this.tasks = [];    // queue with tasks awaiting execution\n\n  options = options || {};\n\n  this.forkArgs = Object.freeze(options.forkArgs || []);\n  this.forkOpts = Object.freeze(options.forkOpts || {});\n  this.workerOpts = Object.freeze(options.workerOpts || {});\n  this.workerThreadOpts = Object.freeze(options.workerThreadOpts || {})\n  this.debugPortStart = (options.debugPortStart || 43210);\n  this.nodeWorker = options.nodeWorker;\n  this.workerType = options.workerType || options.nodeWorker || 'auto'\n  this.maxQueueSize = options.maxQueueSize || Infinity;\n  this.workerTerminateTimeout = options.workerTerminateTimeout || 1000;\n\n  this.onCreateWorker = options.onCreateWorker || (() => null);\n  this.onTerminateWorker = options.onTerminateWorker || (() => null);\n\n  // configuration\n  if (options && 'maxWorkers' in options) {\n    validateMaxWorkers(options.maxWorkers);\n    this.maxWorkers = options.maxWorkers;\n  }\n  else {\n    this.maxWorkers = Math.max((environment.cpus || 4) - 1, 1);\n  }\n\n  if (options && 'minWorkers' in options) {\n    if(options.minWorkers === 'max') {\n      this.minWorkers = this.maxWorkers;\n    } else {\n      validateMinWorkers(options.minWorkers);\n      this.minWorkers = options.minWorkers;\n      this.maxWorkers = Math.max(this.minWorkers, this.maxWorkers);     // in case minWorkers is higher than maxWorkers\n    }\n    this._ensureMinWorkers();\n  }\n\n  this._boundNext = this._next.bind(this);\n\n\n  if (this.workerType === 'thread') {\n    WorkerHandler.ensureWorkerThreads();\n  }\n}\n\n\n/**\n * Execute a function on a worker.\n *\n * Example usage:\n *\n *   var pool = new Pool()\n *\n *   // call a function available on the worker\n *   pool.exec('fibonacci', [6])\n *\n *   // offload a function\n *   function add(a, b) {\n *     return a + b\n *   };\n *   pool.exec(add, [2, 4])\n *       .then(function (result) {\n *         console.log(result); // outputs 6\n *       })\n *       .catch(function(error) {\n *         console.log(error);\n *       });\n *\n * @param {String | Function} method  Function name or function.\n *                                    If `method` is a string, the corresponding\n *                                    method on the worker will be executed\n *                                    If `method` is a Function, the function\n *                                    will be stringified and executed via the\n *                                    workers built-in function `run(fn, args)`.\n * @param {Array} [params]  Function arguments applied when calling the function\n * @param {ExecOptions} [options]  Options object\n * @return {Promise.<*, Error>} result\n */\nPool.prototype.exec = function (method, params, options) {\n  // validate type of arguments\n  if (params && !Array.isArray(params)) {\n    throw new TypeError('Array expected as argument \"params\"');\n  }\n\n  if (typeof method === 'string') {\n    var resolver = Promise.defer();\n\n    if (this.tasks.length >= this.maxQueueSize) {\n      throw new Error('Max queue size of ' + this.maxQueueSize + ' reached');\n    }\n\n    // add a new task to the queue\n    var tasks = this.tasks;\n    var task = {\n      method:  method,\n      params:  params,\n      resolver: resolver,\n      timeout: null,\n      options: options\n    };\n    tasks.push(task);\n\n    // replace the timeout method of the Promise with our own,\n    // which starts the timer as soon as the task is actually started\n    var originalTimeout = resolver.promise.timeout;\n    resolver.promise.timeout = function timeout (delay) {\n      if (tasks.indexOf(task) !== -1) {\n        // task is still queued -> start the timer later on\n        task.timeout = delay;\n        return resolver.promise;\n      }\n      else {\n        // task is already being executed -> start timer immediately\n        return originalTimeout.call(resolver.promise, delay);\n      }\n    };\n\n    // trigger task execution\n    this._next();\n\n    return resolver.promise;\n  }\n  else if (typeof method === 'function') {\n    // send stringified function and function arguments to worker\n    return this.exec('run', [String(method), params]);\n  }\n  else {\n    throw new TypeError('Function or string expected as argument \"method\"');\n  }\n};\n\n/**\n * Create a proxy for current worker. Returns an object containing all\n * methods available on the worker. The methods always return a promise.\n *\n * @return {Promise.<Object, Error>} proxy\n */\nPool.prototype.proxy = function () {\n  if (arguments.length > 0) {\n    throw new Error('No arguments expected');\n  }\n\n  var pool = this;\n  return this.exec('methods')\n      .then(function (methods) {\n        var proxy = {};\n\n        methods.forEach(function (method) {\n          proxy[method] = function () {\n            return pool.exec(method, Array.prototype.slice.call(arguments));\n          }\n        });\n\n        return proxy;\n      });\n};\n\n/**\n * Creates new array with the results of calling a provided callback function\n * on every element in this array.\n * @param {Array} array\n * @param {function} callback  Function taking two arguments:\n *                             `callback(currentValue, index)`\n * @return {Promise.<Array>} Returns a promise which resolves  with an Array\n *                           containing the results of the callback function\n *                           executed for each of the array elements.\n */\n/* TODO: implement map\nPool.prototype.map = function (array, callback) {\n};\n*/\n\n/**\n * Grab the first task from the queue, find a free worker, and assign the\n * worker to the task.\n * @protected\n */\nPool.prototype._next = function () {\n  if (this.tasks.length > 0) {\n    // there are tasks in the queue\n\n    // find an available worker\n    var worker = this._getWorker();\n    if (worker) {\n      // get the first task from the queue\n      var me = this;\n      var task = this.tasks.shift();\n\n      // check if the task is still pending (and not cancelled -> promise rejected)\n      if (task.resolver.promise.pending) {\n        // send the request to the worker\n        var promise = worker.exec(task.method, task.params, task.resolver, task.options)\n          .then(me._boundNext)\n          .catch(function () {\n            // if the worker crashed and terminated, remove it from the pool\n            if (worker.terminated) {\n              return me._removeWorker(worker);\n            }\n          }).then(function() {\n            me._next(); // trigger next task in the queue\n          });\n\n        // start queued timer now\n        if (typeof task.timeout === 'number') {\n          promise.timeout(task.timeout);\n        }\n      } else {\n        // The task taken was already complete (either rejected or resolved), so just trigger next task in the queue\n        me._next();\n      }\n    }\n  }\n};\n\n/**\n * Get an available worker. If no worker is available and the maximum number\n * of workers isn't yet reached, a new worker will be created and returned.\n * If no worker is available and the maximum number of workers is reached,\n * null will be returned.\n *\n * @return {WorkerHandler | null} worker\n * @private\n */\nPool.prototype._getWorker = function() {\n  // find a non-busy worker\n  var workers = this.workers;\n  for (var i = 0; i < workers.length; i++) {\n    var worker = workers[i];\n    if (worker.busy() === false) {\n      return worker;\n    }\n  }\n\n  if (workers.length < this.maxWorkers) {\n    // create a new worker\n    worker = this._createWorkerHandler();\n    workers.push(worker);\n    return worker;\n  }\n\n  return null;\n};\n\n/**\n * Remove a worker from the pool.\n * Attempts to terminate worker if not already terminated, and ensures the minimum\n * pool size is met.\n * @param {WorkerHandler} worker\n * @return {Promise<WorkerHandler>}\n * @protected\n */\nPool.prototype._removeWorker = function(worker) {\n  var me = this;\n\n  DEBUG_PORT_ALLOCATOR.releasePort(worker.debugPort);\n  // _removeWorker will call this, but we need it to be removed synchronously\n  this._removeWorkerFromList(worker);\n  // If minWorkers set, spin up new workers to replace the crashed ones\n  this._ensureMinWorkers();\n  // terminate the worker (if not already terminated)\n  return new Promise(function(resolve, reject) {\n    worker.terminate(false, function(err) {\n      me.onTerminateWorker({\n        forkArgs: worker.forkArgs,\n        forkOpts: worker.forkOpts,\n        workerThreadOpts: worker.workerThreadOpts,\n        script: worker.script\n      });\n      if (err) {\n        reject(err);\n      } else {\n        resolve(worker);\n      }\n    });\n  });\n};\n\n/**\n * Remove a worker from the pool list.\n * @param {WorkerHandler} worker\n * @protected\n */\nPool.prototype._removeWorkerFromList = function(worker) {\n  // remove from the list with workers\n  var index = this.workers.indexOf(worker);\n  if (index !== -1) {\n    this.workers.splice(index, 1);\n  }\n};\n\n/**\n * Close all active workers. Tasks currently being executed will be finished first.\n * @param {boolean} [force=false]   If false (default), the workers are terminated\n *                                  after finishing all tasks currently in\n *                                  progress. If true, the workers will be\n *                                  terminated immediately.\n * @param {number} [timeout]        If provided and non-zero, worker termination promise will be rejected\n *                                  after timeout if worker process has not been terminated.\n * @return {Promise.<void, Error>}\n */\nPool.prototype.terminate = function (force, timeout) {\n  var me = this;\n\n  // cancel any pending tasks\n  this.tasks.forEach(function (task) {\n    task.resolver.reject(new Error('Pool terminated'));\n  });\n  this.tasks.length = 0;\n\n  var f = function (worker) {\n    DEBUG_PORT_ALLOCATOR.releasePort(worker.debugPort);\n    this._removeWorkerFromList(worker);\n  };\n  var removeWorker = f.bind(this);\n\n  var promises = [];\n  var workers = this.workers.slice();\n  workers.forEach(function (worker) {\n    var termPromise = worker.terminateAndNotify(force, timeout)\n      .then(removeWorker)\n      .always(function() {\n        me.onTerminateWorker({\n          forkArgs: worker.forkArgs,\n          forkOpts: worker.forkOpts,\n          workerThreadOpts: worker.workerThreadOpts,\n          script: worker.script\n        });\n      });\n    promises.push(termPromise);\n  });\n  return Promise.all(promises);\n};\n\n/**\n * Retrieve statistics on tasks and workers.\n * @return {{totalWorkers: number, busyWorkers: number, idleWorkers: number, pendingTasks: number, activeTasks: number}} Returns an object with statistics\n */\nPool.prototype.stats = function () {\n  var totalWorkers = this.workers.length;\n  var busyWorkers = this.workers.filter(function (worker) {\n    return worker.busy();\n  }).length;\n\n  return {\n    totalWorkers:  totalWorkers,\n    busyWorkers:   busyWorkers,\n    idleWorkers:   totalWorkers - busyWorkers,\n\n    pendingTasks:  this.tasks.length,\n    activeTasks:   busyWorkers\n  };\n};\n\n/**\n * Ensures that a minimum of minWorkers is up and running\n * @protected\n */\nPool.prototype._ensureMinWorkers = function() {\n  if (this.minWorkers) {\n    for(var i = this.workers.length; i < this.minWorkers; i++) {\n      this.workers.push(this._createWorkerHandler());\n    }\n  }\n};\n\n/**\n * Helper function to create a new WorkerHandler and pass all options.\n * @return {WorkerHandler}\n * @private\n */\nPool.prototype._createWorkerHandler = function () {\n  const overriddenParams = this.onCreateWorker({\n    forkArgs: this.forkArgs,\n    forkOpts: this.forkOpts,\n    workerOpts: this.workerOpts,\n    workerThreadOpts: this.workerThreadOpts,\n    script: this.script\n  }) || {};\n\n  return new WorkerHandler(overriddenParams.script || this.script, {\n    forkArgs: overriddenParams.forkArgs || this.forkArgs,\n    forkOpts: overriddenParams.forkOpts || this.forkOpts,\n    workerOpts: overriddenParams.workerOpts || this.workerOpts,\n    workerThreadOpts: overriddenParams.workerThreadOpts || this.workerThreadOpts,\n    debugPort: DEBUG_PORT_ALLOCATOR.nextAvailableStartingAt(this.debugPortStart),\n    workerType: this.workerType,\n    workerTerminateTimeout: this.workerTerminateTimeout,\n  });\n}\n\n/**\n * Ensure that the maxWorkers option is an integer >= 1\n * @param {*} maxWorkers\n * @returns {boolean} returns true maxWorkers has a valid value\n */\nfunction validateMaxWorkers(maxWorkers) {\n  if (!isNumber(maxWorkers) || !isInteger(maxWorkers) || maxWorkers < 1) {\n    throw new TypeError('Option maxWorkers must be an integer number >= 1');\n  }\n}\n\n/**\n * Ensure that the minWorkers option is an integer >= 0\n * @param {*} minWorkers\n * @returns {boolean} returns true when minWorkers has a valid value\n */\nfunction validateMinWorkers(minWorkers) {\n  if (!isNumber(minWorkers) || !isInteger(minWorkers) || minWorkers < 0) {\n    throw new TypeError('Option minWorkers must be an integer number >= 0');\n  }\n}\n\n/**\n * Test whether a variable is a number\n * @param {*} value\n * @returns {boolean} returns true when value is a number\n */\nfunction isNumber(value) {\n  return typeof value === 'number';\n}\n\n/**\n * Test whether a number is an integer\n * @param {number} value\n * @returns {boolean} Returns true if value is an integer\n */\nfunction isInteger(value) {\n  return Math.round(value) == value;\n}\n\nmodule.exports = Pool;\n", "'use strict';\n\n/**\n * Promise\n *\n * Inspired by https://gist.github.com/RubaXa/8501359 from RubaXa <<EMAIL>>\n *\n * @param {Function} handler   Called as handler(resolve: Function, reject: Function)\n * @param {Promise} [parent]   Parent promise for propagation of cancel and timeout\n */\nfunction Promise(handler, parent) {\n  var me = this;\n\n  if (!(this instanceof Promise)) {\n    throw new SyntaxError('Constructor must be called with the new operator');\n  }\n\n  if (typeof handler !== 'function') {\n    throw new SyntaxError('Function parameter handler(resolve, reject) missing');\n  }\n\n  var _onSuccess = [];\n  var _onFail = [];\n\n  // status\n  this.resolved = false;\n  this.rejected = false;\n  this.pending = true;\n\n  /**\n   * Process onSuccess and onFail callbacks: add them to the queue.\n   * Once the promise is resolve, the function _promise is replace.\n   * @param {Function} onSuccess\n   * @param {Function} onFail\n   * @private\n   */\n  var _process = function (onSuccess, onFail) {\n    _onSuccess.push(onSuccess);\n    _onFail.push(onFail);\n  };\n\n  /**\n   * Add an onSuccess callback and optionally an onFail callback to the Promise\n   * @param {Function} onSuccess\n   * @param {Function} [onFail]\n   * @returns {Promise} promise\n   */\n  this.then = function (onSuccess, onFail) {\n    return new Promise(function (resolve, reject) {\n      var s = onSuccess ? _then(onSuccess, resolve, reject) : resolve;\n      var f = onFail    ? _then(onFail,    resolve, reject) : reject;\n\n      _process(s, f);\n    }, me);\n  };\n\n  /**\n   * Resolve the promise\n   * @param {*} result\n   * @type {Function}\n   */\n  var _resolve = function (result) {\n    // update status\n    me.resolved = true;\n    me.rejected = false;\n    me.pending = false;\n\n    _onSuccess.forEach(function (fn) {\n      fn(result);\n    });\n\n    _process = function (onSuccess, onFail) {\n      onSuccess(result);\n    };\n\n    _resolve = _reject = function () { };\n\n    return me;\n  };\n\n  /**\n   * Reject the promise\n   * @param {Error} error\n   * @type {Function}\n   */\n  var _reject = function (error) {\n    // update status\n    me.resolved = false;\n    me.rejected = true;\n    me.pending = false;\n\n    _onFail.forEach(function (fn) {\n      fn(error);\n    });\n\n    _process = function (onSuccess, onFail) {\n      onFail(error);\n    };\n\n    _resolve = _reject = function () { }\n\n    return me;\n  };\n\n  /**\n   * Cancel te promise. This will reject the promise with a CancellationError\n   * @returns {Promise} self\n   */\n  this.cancel = function () {\n    if (parent) {\n      parent.cancel();\n    }\n    else {\n      _reject(new CancellationError());\n    }\n\n    return me;\n  };\n\n  /**\n   * Set a timeout for the promise. If the promise is not resolved within\n   * the time, the promise will be cancelled and a TimeoutError is thrown.\n   * If the promise is resolved in time, the timeout is removed.\n   * @param {number} delay     Delay in milliseconds\n   * @returns {Promise} self\n   */\n  this.timeout = function (delay) {\n    if (parent) {\n      parent.timeout(delay);\n    }\n    else {\n      var timer = setTimeout(function () {\n        _reject(new TimeoutError('Promise timed out after ' + delay + ' ms'));\n      }, delay);\n\n      me.always(function () {\n        clearTimeout(timer);\n      });\n    }\n\n    return me;\n  };\n\n  // attach handler passing the resolve and reject functions\n  handler(function (result) {\n    _resolve(result);\n  }, function (error) {\n    _reject(error);\n  });\n}\n\n/**\n * Execute given callback, then call resolve/reject based on the returned result\n * @param {Function} callback\n * @param {Function} resolve\n * @param {Function} reject\n * @returns {Function}\n * @private\n */\nfunction _then(callback, resolve, reject) {\n  return function (result) {\n    try {\n      var res = callback(result);\n      if (res && typeof res.then === 'function' && typeof res['catch'] === 'function') {\n        // method returned a promise\n        res.then(resolve, reject);\n      }\n      else {\n        resolve(res);\n      }\n    }\n    catch (error) {\n      reject(error);\n    }\n  }\n}\n\n/**\n * Add an onFail callback to the Promise\n * @param {Function} onFail\n * @returns {Promise} promise\n */\nPromise.prototype['catch'] = function (onFail) {\n  return this.then(null, onFail);\n};\n\n// TODO: add support for Promise.catch(Error, callback)\n// TODO: add support for Promise.catch(Error, Error, callback)\n\n/**\n * Execute given callback when the promise either resolves or rejects.\n * @param {Function} fn\n * @returns {Promise} promise\n */\nPromise.prototype.always = function (fn) {\n  return this.then(fn, fn);\n};\n\n/**\n * Create a promise which resolves when all provided promises are resolved,\n * and fails when any of the promises resolves.\n * @param {Promise[]} promises\n * @returns {Promise} promise\n */\nPromise.all = function (promises){\n  return new Promise(function (resolve, reject) {\n    var remaining = promises.length,\n        results = [];\n\n    if (remaining) {\n      promises.forEach(function (p, i) {\n        p.then(function (result) {\n          results[i] = result;\n          remaining--;\n          if (remaining == 0) {\n            resolve(results);\n          }\n        }, function (error) {\n          remaining = 0;\n          reject(error);\n        });\n      });\n    }\n    else {\n      resolve(results);\n    }\n  });\n};\n\n/**\n * Create a promise resolver\n * @returns {{promise: Promise, resolve: Function, reject: Function}} resolver\n */\nPromise.defer = function () {\n  var resolver = {};\n\n  resolver.promise = new Promise(function (resolve, reject) {\n    resolver.resolve = resolve;\n    resolver.reject = reject;\n  });\n\n  return resolver;\n};\n\n/**\n * Create a cancellation error\n * @param {String} [message]\n * @extends Error\n */\nfunction CancellationError(message) {\n  this.message = message || 'promise cancelled';\n  this.stack = (new Error()).stack;\n}\n\nCancellationError.prototype = new Error();\nCancellationError.prototype.constructor = Error;\nCancellationError.prototype.name = 'CancellationError';\n\nPromise.CancellationError = CancellationError;\n\n\n/**\n * Create a timeout error\n * @param {String} [message]\n * @extends Error\n */\nfunction TimeoutError(message) {\n  this.message = message || 'timeout exceeded';\n  this.stack = (new Error()).stack;\n}\n\nTimeoutError.prototype = new Error();\nTimeoutError.prototype.constructor = Error;\nTimeoutError.prototype.name = 'TimeoutError';\n\nPromise.TimeoutError = TimeoutError;\n\n\nmodule.exports = Promise;\n", "'use strict';\n\nvar Promise = require('./Promise');\nvar environment = require('./environment');\nvar requireFoolWebpack = require('./requireFoolWebpack');\n\n/**\n * Special message sent by parent which causes a child process worker to terminate itself.\n * Not a \"message object\"; this string is the entire message.\n */\nvar TERMINATE_METHOD_ID = '__workerpool-terminate__';\n\nfunction ensureWorkerThreads() {\n  var WorkerThreads = tryRequireWorkerThreads()\n  if (!WorkerThreads) {\n    throw new Error('WorkerPool: workerType = \\'thread\\' is not supported, Node >= 11.7.0 required')\n  }\n\n  return WorkerThreads;\n}\n\n// check whether Worker is supported by the browser\nfunction ensureWebWorker() {\n  // Workaround for a bug in PhantomJS (Or QtWebkit): https://github.com/ariya/phantomjs/issues/14534\n  if (typeof Worker !== 'function' && (typeof Worker !== 'object' || typeof Worker.prototype.constructor !== 'function')) {\n    throw new Error('WorkerPool: Web Workers not supported');\n  }\n}\n\nfunction tryRequireWorkerThreads() {\n  try {\n    return requireFoolWebpack('worker_threads');\n  } catch(error) {\n    if (typeof error === 'object' && error !== null && error.code === 'MODULE_NOT_FOUND') {\n      // no worker_threads available (old version of node.js)\n      return null;\n    } else {\n      throw error;\n    }\n  }\n}\n\n// get the default worker script\nfunction getDefaultWorker() {\n  if (environment.platform === 'browser') {\n    // test whether the browser supports all features that we need\n    if (typeof Blob === 'undefined') {\n      throw new Error('Blob not supported by the browser');\n    }\n    if (!window.URL || typeof window.URL.createObjectURL !== 'function') {\n      throw new Error('URL.createObjectURL not supported by the browser');\n    }\n\n    // use embedded worker.js\n    var blob = new Blob([require('./generated/embeddedWorker')], {type: 'text/javascript'});\n    return window.URL.createObjectURL(blob);\n  }\n  else {\n    // use external worker.js in current directory\n    return __dirname + '/worker.js';\n  }\n}\n\nfunction setupWorker(script, options) {\n  if (options.workerType === 'web') { // browser only\n    ensureWebWorker();\n    return setupBrowserWorker(script, options.workerOpts, Worker);\n  } else if (options.workerType === 'thread') { // node.js only\n    WorkerThreads = ensureWorkerThreads();\n    return setupWorkerThreadWorker(script, WorkerThreads, options.workerThreadOpts);\n  } else if (options.workerType === 'process' || !options.workerType) { // node.js only\n    return setupProcessWorker(script, resolveForkOptions(options), requireFoolWebpack('child_process'));\n  } else { // options.workerType === 'auto' or undefined\n    if (environment.platform === 'browser') {\n      ensureWebWorker();\n      return setupBrowserWorker(script, options.workerOpts, Worker);\n    }\n    else { // environment.platform === 'node'\n      var WorkerThreads = tryRequireWorkerThreads();\n      if (WorkerThreads) {\n        return setupWorkerThreadWorker(script, WorkerThreads, options.workerThreadOpts);\n      } else {\n        return setupProcessWorker(script, resolveForkOptions(options), requireFoolWebpack('child_process'));\n      }\n    }\n  }\n}\n\nfunction setupBrowserWorker(script, workerOpts, Worker) {\n  // create the web worker\n  var worker = new Worker(script, workerOpts);\n\n  worker.isBrowserWorker = true;\n  // add node.js API to the web worker\n  worker.on = function (event, callback) {\n    this.addEventListener(event, function (message) {\n      callback(message.data);\n    });\n  };\n  worker.send = function (message, transfer) {\n    this.postMessage(message, transfer);\n  };\n  return worker;\n}\n\nfunction setupWorkerThreadWorker(script, WorkerThreads, workerThreadOptions) {\n  var worker = new WorkerThreads.Worker(script, {\n    stdout: false, // automatically pipe worker.STDOUT to process.STDOUT\n    stderr: false,  // automatically pipe worker.STDERR to process.STDERR\n    ...workerThreadOptions\n  });\n  worker.isWorkerThread = true;\n  worker.send = function(message, transfer) {\n    this.postMessage(message, transfer);\n  };\n\n  worker.kill = function() {\n    this.terminate();\n    return true;\n  };\n\n  worker.disconnect = function() {\n    this.terminate();\n  };\n\n  return worker;\n}\n\nfunction setupProcessWorker(script, options, child_process) {\n  // no WorkerThreads, fallback to sub-process based workers\n  var worker = child_process.fork(\n    script,\n    options.forkArgs,\n    options.forkOpts\n  );\n\n  // ignore transfer argument since it is not supported by process\n  var send = worker.send;\n  worker.send = function (message) {\n    return send.call(worker, message);\n  };\n\n  worker.isChildProcess = true;\n  return worker;\n}\n\n// add debug flags to child processes if the node inspector is active\nfunction resolveForkOptions(opts) {\n  opts = opts || {};\n\n  var processExecArgv = process.execArgv.join(' ');\n  var inspectorActive = processExecArgv.indexOf('--inspect') !== -1;\n  var debugBrk = processExecArgv.indexOf('--debug-brk') !== -1;\n\n  var execArgv = [];\n  if (inspectorActive) {\n    execArgv.push('--inspect=' + opts.debugPort);\n\n    if (debugBrk) {\n      execArgv.push('--debug-brk');\n    }\n  }\n\n  process.execArgv.forEach(function(arg) {\n    if (arg.indexOf('--max-old-space-size') > -1) {\n      execArgv.push(arg)\n    }\n  })\n\n  return Object.assign({}, opts, {\n    forkArgs: opts.forkArgs,\n    forkOpts: Object.assign({}, opts.forkOpts, {\n      execArgv: (opts.forkOpts && opts.forkOpts.execArgv || [])\n      .concat(execArgv)\n    })\n  });\n}\n\n/**\n * Converts a serialized error to Error\n * @param {Object} obj Error that has been serialized and parsed to object\n * @return {Error} The equivalent Error.\n */\nfunction objectToError (obj) {\n  var temp = new Error('')\n  var props = Object.keys(obj)\n\n  for (var i = 0; i < props.length; i++) {\n    temp[props[i]] = obj[props[i]]\n  }\n\n  return temp\n}\n\n/**\n * A WorkerHandler controls a single worker. This worker can be a child process\n * on node.js or a WebWorker in a browser environment.\n * @param {String} [script] If no script is provided, a default worker with a\n *                          function run will be created.\n * @param {WorkerPoolOptions} _options See docs\n * @constructor\n */\nfunction WorkerHandler(script, _options) {\n  var me = this;\n  var options = _options || {};\n\n  this.script = script || getDefaultWorker();\n  this.worker = setupWorker(this.script, options);\n  this.debugPort = options.debugPort;\n  this.forkOpts = options.forkOpts;\n  this.forkArgs = options.forkArgs;\n  this.workerOpts = options.workerOpts;\n  this.workerThreadOpts = options.workerThreadOpts\n  this.workerTerminateTimeout = options.workerTerminateTimeout;\n\n  // The ready message is only sent if the worker.add method is called (And the default script is not used)\n  if (!script) {\n    this.worker.ready = true;\n  }\n\n  // queue for requests that are received before the worker is ready\n  this.requestQueue = [];\n  this.worker.on('message', function (response) {\n    if (me.terminated) {\n      return;\n    }\n    if (typeof response === 'string' && response === 'ready') {\n      me.worker.ready = true;\n      dispatchQueuedRequests();\n    } else {\n      // find the task from the processing queue, and run the tasks callback\n      var id = response.id;\n      var task = me.processing[id];\n      if (task !== undefined) {\n        if (response.isEvent) {\n          if (task.options && typeof task.options.on === 'function') {\n            task.options.on(response.payload);\n          }\n        } else {\n          // remove the task from the queue\n          delete me.processing[id];\n\n          // test if we need to terminate\n          if (me.terminating === true) {\n            // complete worker termination if all tasks are finished\n            me.terminate();\n          }\n\n          // resolve the task's promise\n          if (response.error) {\n            task.resolver.reject(objectToError(response.error));\n          }\n          else {\n            task.resolver.resolve(response.result);\n          }\n        }\n      }\n    }\n  });\n\n  // reject all running tasks on worker error\n  function onError(error) {\n    me.terminated = true;\n\n    for (var id in me.processing) {\n      if (me.processing[id] !== undefined) {\n        me.processing[id].resolver.reject(error);\n      }\n    }\n    me.processing = Object.create(null);\n  }\n\n  // send all queued requests to worker\n  function dispatchQueuedRequests()\n  {\n    for(const request of me.requestQueue.splice(0)) {\n      me.worker.send(request.message, request.transfer);\n    }\n  }\n\n  var worker = this.worker;\n  // listen for worker messages error and exit\n  this.worker.on('error', onError);\n  this.worker.on('exit', function (exitCode, signalCode) {\n    var message = 'Workerpool Worker terminated Unexpectedly\\n';\n\n    message += '    exitCode: `' + exitCode + '`\\n';\n    message += '    signalCode: `' + signalCode + '`\\n';\n\n    message += '    workerpool.script: `' +  me.script + '`\\n';\n    message += '    spawnArgs: `' +  worker.spawnargs + '`\\n';\n    message += '    spawnfile: `' + worker.spawnfile + '`\\n'\n\n    message += '    stdout: `' + worker.stdout + '`\\n'\n    message += '    stderr: `' + worker.stderr + '`\\n'\n\n    onError(new Error(message));\n  });\n\n  this.processing = Object.create(null); // queue with tasks currently in progress\n\n  this.terminating = false;\n  this.terminated = false;\n  this.cleaning = false;\n  this.terminationHandler = null;\n  this.lastId = 0;\n}\n\n/**\n * Get a list with methods available on the worker.\n * @return {Promise.<String[], Error>} methods\n */\nWorkerHandler.prototype.methods = function () {\n  return this.exec('methods');\n};\n\n/**\n * Execute a method with given parameters on the worker\n * @param {String} method\n * @param {Array} [params]\n * @param {{resolve: Function, reject: Function}} [resolver]\n * @param {ExecOptions}  [options]\n * @return {Promise.<*, Error>} result\n */\nWorkerHandler.prototype.exec = function(method, params, resolver, options) {\n  if (!resolver) {\n    resolver = Promise.defer();\n  }\n\n  // generate a unique id for the task\n  var id = ++this.lastId;\n\n  // register a new task as being in progress\n  this.processing[id] = {\n    id: id,\n    resolver: resolver,\n    options: options\n  };\n\n  // build a JSON-RPC request\n  var request = {\n    message: {\n      id: id,\n      method: method,\n      params: params\n    },\n    transfer: options && options.transfer\n  };\n\n  if (this.terminated) {\n    resolver.reject(new Error('Worker is terminated'));\n  } else if (this.worker.ready) {\n    // send the request to the worker\n    this.worker.send(request.message, request.transfer);\n  } else {\n    this.requestQueue.push(request);\n  }\n\n  // on cancellation, force the worker to terminate\n  var me = this;\n  return resolver.promise.catch(function (error) {\n    if (error instanceof Promise.CancellationError || error instanceof Promise.TimeoutError) {\n      // remove this task from the queue. It is already rejected (hence this\n      // catch event), and else it will be rejected again when terminating\n      delete me.processing[id];\n\n      // terminate worker\n      return me.terminateAndNotify(true)\n        .then(function() {\n          throw error;\n        }, function(err) {\n          throw err;\n        });\n    } else {\n      throw error;\n    }\n  })\n};\n\n/**\n * Test whether the worker is processing any tasks or cleaning up before termination.\n * @return {boolean} Returns true if the worker is busy\n */\nWorkerHandler.prototype.busy = function () {\n  return this.cleaning || Object.keys(this.processing).length > 0;\n};\n\n/**\n * Terminate the worker.\n * @param {boolean} [force=false]   If false (default), the worker is terminated\n *                                  after finishing all tasks currently in\n *                                  progress. If true, the worker will be\n *                                  terminated immediately.\n * @param {function} [callback=null] If provided, will be called when process terminates.\n */\nWorkerHandler.prototype.terminate = function (force, callback) {\n  var me = this;\n  if (force) {\n    // cancel all tasks in progress\n    for (var id in this.processing) {\n      if (this.processing[id] !== undefined) {\n        this.processing[id].resolver.reject(new Error('Worker terminated'));\n      }\n    }\n    this.processing = Object.create(null);\n  }\n\n  if (typeof callback === 'function') {\n    this.terminationHandler = callback;\n  }\n  if (!this.busy()) {\n    // all tasks are finished. kill the worker\n    var cleanup = function(err) {\n      me.terminated = true;\n      me.cleaning = false;\n      if (me.worker != null && me.worker.removeAllListeners) {\n        // removeAllListeners is only available for child_process\n        me.worker.removeAllListeners('message');\n      }\n      me.worker = null;\n      me.terminating = false;\n      if (me.terminationHandler) {\n        me.terminationHandler(err, me);\n      } else if (err) {\n        throw err;\n      }\n    }\n\n    if (this.worker) {\n      if (typeof this.worker.kill === 'function') {\n        if (this.worker.killed) {\n          cleanup(new Error('worker already killed!'));\n          return;\n        }\n\n        // child process and worker threads\n        var cleanExitTimeout = setTimeout(function() {\n          if (me.worker) {\n            me.worker.kill();\n          }\n        }, this.workerTerminateTimeout);\n\n        this.worker.once('exit', function() {\n          clearTimeout(cleanExitTimeout);\n          if (me.worker) {\n            me.worker.killed = true;\n          }\n          cleanup();\n        });\n\n        if (this.worker.ready) {\n          this.worker.send(TERMINATE_METHOD_ID);\n        } else {\n          this.requestQueue.push({ message: TERMINATE_METHOD_ID });\n        }\n\n        // mark that the worker is cleaning up resources\n        // to prevent new tasks from being executed\n        this.cleaning = true;\n        return;\n      }\n      else if (typeof this.worker.terminate === 'function') {\n        this.worker.terminate(); // web worker\n        this.worker.killed = true;\n      }\n      else {\n        throw new Error('Failed to terminate worker');\n      }\n    }\n    cleanup();\n  }\n  else {\n    // we can't terminate immediately, there are still tasks being executed\n    this.terminating = true;\n  }\n};\n\n/**\n * Terminate the worker, returning a Promise that resolves when the termination has been done.\n * @param {boolean} [force=false]   If false (default), the worker is terminated\n *                                  after finishing all tasks currently in\n *                                  progress. If true, the worker will be\n *                                  terminated immediately.\n * @param {number} [timeout]        If provided and non-zero, worker termination promise will be rejected\n *                                  after timeout if worker process has not been terminated.\n * @return {Promise.<WorkerHandler, Error>}\n */\nWorkerHandler.prototype.terminateAndNotify = function (force, timeout) {\n  var resolver = Promise.defer();\n  if (timeout) {\n    resolver.promise.timeout(timeout);\n  }\n  this.terminate(force, function(err, worker) {\n    if (err) {\n      resolver.reject(err);\n    } else {\n      resolver.resolve(worker);\n    }\n  });\n  return resolver.promise;\n};\n\nmodule.exports = WorkerHandler;\nmodule.exports._tryRequireWorkerThreads = tryRequireWorkerThreads;\nmodule.exports._setupProcessWorker = setupProcessWorker;\nmodule.exports._setupBrowserWorker = setupBrowserWorker;\nmodule.exports._setupWorkerThreadWorker = setupWorkerThreadWorker;\nmodule.exports.ensureWorkerThreads = ensureWorkerThreads;\n", "'use strict';\n\nvar MAX_PORTS = 65535;\nmodule.exports = DebugPortAllocator;\nfunction DebugPortAllocator() {\n  this.ports = Object.create(null);\n  this.length = 0;\n}\n\nDebugPortAllocator.prototype.nextAvailableStartingAt = function(starting) {\n  while (this.ports[starting] === true) {\n    starting++;\n  }\n\n  if (starting >= MAX_PORTS) {\n    throw new Error('WorkerPool debug port limit reached: ' + starting + '>= ' + MAX_PORTS );\n  }\n\n  this.ports[starting] = true;\n  this.length++;\n  return starting;\n};\n\nDebugPortAllocator.prototype.releasePort = function(port) {\n  delete this.ports[port];\n  this.length--;\n};\n\n", "var requireFoolWebpack = require('./requireFoolWebpack');\n\n// source: https://github.com/flexdinesh/browser-or-node\nvar isNode = function (nodeProcess) {\n  return (\n    typeof nodeProcess !== 'undefined' &&\n    nodeProcess.versions != null &&\n    nodeProcess.versions.node != null\n  );\n}\nmodule.exports.isNode = isNode\n\n// determines the JavaScript platform: browser or node\nmodule.exports.platform = typeof process !== 'undefined' && isNode(process)\n  ? 'node'\n  : 'browser';\n\n// determines whether the code is running in main thread or not\n// note that in node.js we have to check both worker_thread and child_process\nvar worker_threads = tryRequireFoolWebpack('worker_threads');\nmodule.exports.isMainThread = module.exports.platform === 'node'\n  ? ((!worker_threads || worker_threads.isMainThread) && !process.connected)\n  : typeof Window !== 'undefined';\n\n// determines the number of cpus available\nmodule.exports.cpus = module.exports.platform === 'browser'\n  ? self.navigator.hardwareConcurrency\n  : requireFoolWebpack('os').cpus().length;\n\nfunction tryRequireFoolWebpack (module) {\n  try {\n    return requireFoolWebpack(module);\n  } catch(err) {\n    return null\n  }\n}\n", "/**\n * embeddedWorker.js contains an embedded version of worker.js.\n * This file is automatically generated,\n * changes made in this file will be overwritten.\n */\nmodule.exports = \"!function(){var __webpack_modules__={577:function(e){e.exports=function(e,r){this.message=e,this.transfer=r}}},__webpack_module_cache__={};function __webpack_require__(e){var r=__webpack_module_cache__[e];return void 0!==r||(r=__webpack_module_cache__[e]={exports:{}},__webpack_modules__[e](r,r.exports,__webpack_require__)),r.exports}var __webpack_exports__={};!function(){var exports=__webpack_exports__,__webpack_unused_export__;function _typeof(e){return(_typeof=\\\"function\\\"==typeof Symbol&&\\\"symbol\\\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\\\"function\\\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\\\"symbol\\\":typeof e})(e)}var Transfer=__webpack_require__(577),requireFoolWebpack=eval(\\\"typeof require !== 'undefined' ? require : function (module) { throw new Error('Module \\\\\\\" + module + \\\\\\\" not found.') }\\\"),TERMINATE_METHOD_ID=\\\"__workerpool-terminate__\\\",worker={exit:function(){}},WorkerThreads,parentPort;if(\\\"undefined\\\"!=typeof self&&\\\"function\\\"==typeof postMessage&&\\\"function\\\"==typeof addEventListener)worker.on=function(e,r){addEventListener(e,function(e){r(e.data)})},worker.send=function(e){postMessage(e)};else{if(\\\"undefined\\\"==typeof process)throw new Error(\\\"Script must be executed as a worker\\\");try{WorkerThreads=requireFoolWebpack(\\\"worker_threads\\\")}catch(error){if(\\\"object\\\"!==_typeof(error)||null===error||\\\"MODULE_NOT_FOUND\\\"!==error.code)throw error}WorkerThreads&&null!==WorkerThreads.parentPort?(parentPort=WorkerThreads.parentPort,worker.send=parentPort.postMessage.bind(parentPort),worker.on=parentPort.on.bind(parentPort)):(worker.on=process.on.bind(process),worker.send=function(e){process.send(e)},worker.on(\\\"disconnect\\\",function(){process.exit(1)})),worker.exit=process.exit.bind(process)}function convertError(o){return Object.getOwnPropertyNames(o).reduce(function(e,r){return Object.defineProperty(e,r,{value:o[r],enumerable:!0})},{})}function isPromise(e){return e&&\\\"function\\\"==typeof e.then&&\\\"function\\\"==typeof e.catch}worker.methods={},worker.methods.run=function(e,r){e=new Function(\\\"return (\\\"+e+\\\").apply(null, arguments);\\\");return e.apply(e,r)},worker.methods.methods=function(){return Object.keys(worker.methods)},worker.terminationHandler=void 0,worker.cleanupAndExit=function(e){function r(){worker.exit(e)}if(!worker.terminationHandler)return r();var o=worker.terminationHandler(e);isPromise(o)?o.then(r,r):r()};var currentRequestId=null;worker.on(\\\"message\\\",function(r){if(r===TERMINATE_METHOD_ID)return worker.cleanupAndExit(0);try{var e=worker.methods[r.method];if(!e)throw new Error('Unknown method \\\"'+r.method+'\\\"');currentRequestId=r.id;var o=e.apply(e,r.params);isPromise(o)?o.then(function(e){e instanceof Transfer?worker.send({id:r.id,result:e.message,error:null},e.transfer):worker.send({id:r.id,result:e,error:null}),currentRequestId=null}).catch(function(e){worker.send({id:r.id,result:null,error:convertError(e)}),currentRequestId=null}):(o instanceof Transfer?worker.send({id:r.id,result:o.message,error:null},o.transfer):worker.send({id:r.id,result:o,error:null}),currentRequestId=null)}catch(e){worker.send({id:r.id,result:null,error:convertError(e)})}}),worker.register=function(e,r){if(e)for(var o in e)e.hasOwnProperty(o)&&(worker.methods[o]=e[o]);r&&(worker.terminationHandler=r.onTerminate),worker.send(\\\"ready\\\")},worker.emit=function(e){currentRequestId&&(e instanceof Transfer?worker.send({id:currentRequestId,isEvent:!0,payload:e.message},e.transfer):worker.send({id:currentRequestId,isEvent:!0,payload:e}))},__webpack_unused_export__=worker.register,worker.emit}()}();\";\n", "// source of inspiration: https://github.com/sindresorhus/require-fool-webpack\nvar requireFoolWebpack = eval(\n    'typeof require !== \\'undefined\\' ' +\n    '? require ' +\n    ': function (module) { throw new Error(\\'Module \" + module + \" not found.\\') }'\n);\n\nmodule.exports = requireFoolWebpack;\n", "/**\n * The helper class for transferring data from the worker to the main thread.\n *\n * @param {Object} message The object to deliver to the main thread.\n * @param {Object[]} transfer An array of transferable Objects to transfer ownership of.\n */\nfunction Transfer(message, transfer) {\n  this.message = message;\n  this.transfer = transfer;\n}\n\nmodule.exports = Transfer;\n", "/**\n * worker must be started as a child process or a web worker.\n * It listens for RPC messages from the parent process.\n */\nvar Transfer = require('./transfer');\n\n// source of inspiration: https://github.com/sindresorhus/require-fool-webpack\nvar requireFoolWebpack = eval(\n    'typeof require !== \\'undefined\\'' +\n    ' ? require' +\n    ' : function (module) { throw new Error(\\'Module \" + module + \" not found.\\') }'\n);\n\n/**\n * Special message sent by parent which causes the worker to terminate itself.\n * Not a \"message object\"; this string is the entire message.\n */\nvar TERMINATE_METHOD_ID = '__workerpool-terminate__';\n\n// var nodeOSPlatform = require('./environment').nodeOSPlatform;\n\n// create a worker API for sending and receiving messages which works both on\n// node.js and in the browser\nvar worker = {\n  exit: function() {}\n};\nif (typeof self !== 'undefined' && typeof postMessage === 'function' && typeof addEventListener === 'function') {\n  // worker in the browser\n  worker.on = function (event, callback) {\n    addEventListener(event, function (message) {\n      callback(message.data);\n    })\n  };\n  worker.send = function (message) {\n    postMessage(message);\n  };\n}\nelse if (typeof process !== 'undefined') {\n  // node.js\n\n  var WorkerThreads;\n  try {\n    WorkerThreads = requireFoolWebpack('worker_threads');\n  } catch(error) {\n    if (typeof error === 'object' && error !== null && error.code === 'MODULE_NOT_FOUND') {\n      // no worker_threads, fallback to sub-process based workers\n    } else {\n      throw error;\n    }\n  }\n\n  if (WorkerThreads &&\n    /* if there is a parentPort, we are in a WorkerThread */\n    WorkerThreads.parentPort !== null) {\n    var parentPort  = WorkerThreads.parentPort;\n    worker.send = parentPort.postMessage.bind(parentPort);\n    worker.on = parentPort.on.bind(parentPort);\n    worker.exit = process.exit.bind(process);\n  } else {\n    worker.on = process.on.bind(process);\n    // ignore transfer argument since it is not supported by process\n    worker.send = function (message) {\n      process.send(message);\n    };\n    // register disconnect handler only for subprocess worker to exit when parent is killed unexpectedly\n    worker.on('disconnect', function () {\n      process.exit(1);\n    });\n    worker.exit = process.exit.bind(process);\n  }\n}\nelse {\n  throw new Error('Script must be executed as a worker');\n}\n\nfunction convertError(error) {\n  return Object.getOwnPropertyNames(error).reduce(function(product, name) {\n    return Object.defineProperty(product, name, {\n\tvalue: error[name],\n\tenumerable: true\n    });\n  }, {});\n}\n\n/**\n * Test whether a value is a Promise via duck typing.\n * @param {*} value\n * @returns {boolean} Returns true when given value is an object\n *                    having functions `then` and `catch`.\n */\nfunction isPromise(value) {\n  return value && (typeof value.then === 'function') && (typeof value.catch === 'function');\n}\n\n// functions available externally\nworker.methods = {};\n\n/**\n * Execute a function with provided arguments\n * @param {String} fn     Stringified function\n * @param {Array} [args]  Function arguments\n * @returns {*}\n */\nworker.methods.run = function run(fn, args) {\n  var f = new Function('return (' + fn + ').apply(null, arguments);');\n  return f.apply(f, args);\n};\n\n/**\n * Get a list with methods available on this worker\n * @return {String[]} methods\n */\nworker.methods.methods = function methods() {\n  return Object.keys(worker.methods);\n};\n\n/**\n * Custom handler for when the worker is terminated.\n */\nworker.terminationHandler = undefined;\n\n/**\n * Cleanup and exit the worker.\n * @param {Number} code \n * @returns \n */\nworker.cleanupAndExit = function(code) {\n  var _exit = function() {\n    worker.exit(code);\n  }\n\n  if(!worker.terminationHandler) {\n    return _exit();\n  }\n\n  var result = worker.terminationHandler(code);\n  if (isPromise(result)) {\n    result.then(_exit, _exit);\n  } else {\n    _exit();\n  }\n}\n\nvar currentRequestId = null;\n\nworker.on('message', function (request) {\n  if (request === TERMINATE_METHOD_ID) {\n    return worker.cleanupAndExit(0);\n  }\n  try {\n    var method = worker.methods[request.method];\n\n    if (method) {\n      currentRequestId = request.id;\n      \n      // execute the function\n      var result = method.apply(method, request.params);\n\n      if (isPromise(result)) {\n        // promise returned, resolve this and then return\n        result\n            .then(function (result) {\n              if (result instanceof Transfer) {\n                worker.send({\n                  id: request.id,\n                  result: result.message,\n                  error: null\n                }, result.transfer);\n              } else {\n                worker.send({\n                  id: request.id,\n                  result: result,\n                  error: null\n                });\n              }\n              currentRequestId = null;\n            })\n            .catch(function (err) {\n              worker.send({\n                id: request.id,\n                result: null,\n                error: convertError(err)\n              });\n              currentRequestId = null;\n            });\n      }\n      else {\n        // immediate result\n        if (result instanceof Transfer) {\n          worker.send({\n            id: request.id,\n            result: result.message,\n            error: null\n          }, result.transfer);\n        } else {\n          worker.send({\n            id: request.id,\n            result: result,\n            error: null\n          });\n        }\n\n        currentRequestId = null;\n      }\n    }\n    else {\n      throw new Error('Unknown method \"' + request.method + '\"');\n    }\n  }\n  catch (err) {\n    worker.send({\n      id: request.id,\n      result: null,\n      error: convertError(err)\n    });\n  }\n});\n\n/**\n * Register methods to the worker\n * @param {Object} [methods]\n * @param {WorkerRegisterOptions} [options]\n */\nworker.register = function (methods, options) {\n\n  if (methods) {\n    for (var name in methods) {\n      if (methods.hasOwnProperty(name)) {\n        worker.methods[name] = methods[name];\n      }\n    }\n  }\n\n  if (options) {\n    worker.terminationHandler = options.onTerminate;\n  }\n\n  worker.send('ready');\n};\n\nworker.emit = function (payload) {\n  if (currentRequestId) {\n    if (payload instanceof Transfer) {\n      worker.send({\n        id: currentRequestId,\n        isEvent: true,\n        payload: payload.message\n      }, payload.transfer);\n      return;\n    }\n\n    worker.send({\n      id: currentRequestId,\n      isEvent: true,\n      payload\n    });\n  }\n};\n\nif (typeof exports !== 'undefined') {\n  exports.add = worker.register;\n  exports.emit = worker.emit;\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "var environment = require('./environment');\n\n/**\n * Create a new worker pool\n * @param {string} [script]\n * @param {WorkerPoolOptions} [options]\n * @returns {Pool} pool\n */\nexports.pool = function pool(script, options) {\n  var Pool = require('./Pool');\n\n  return new Pool(script, options);\n};\n\n/**\n * Create a worker and optionally register a set of methods to the worker.\n * @param {Object} [methods]\n * @param {WorkerRegisterOptions} [options]\n */\nexports.worker = function worker(methods, options) {\n  var worker = require('./worker');\n  worker.add(methods, options);\n};\n\n/**\n * Sends an event to the parent worker pool.\n * @param {any} payload \n */\nexports.workerEmit = function workerEmit(payload) {\n  var worker = require('./worker');\n  worker.emit(payload);\n};\n\n/**\n * Create a promise.\n * @type {Promise} promise\n */\nexports.Promise = require('./Promise');\n\n/**\n * Create a transfer object.\n * @type {Transfer} transfer\n */\nexports.Transfer = require('./transfer');\n\nexports.platform = environment.platform;\nexports.isMainThread = environment.isMainThread;\nexports.cpus = environment.cpus;"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "this", "Promise", "require", "Worker<PERSON><PERSON>ler", "environment", "DEBUG_PORT_ALLOCATOR", "Pool", "script", "options", "workers", "tasks", "forkArgs", "Object", "freeze", "forkOpts", "workerOpts", "workerThreadOpts", "debugPortStart", "nodeWorker", "workerType", "maxQueueSize", "Infinity", "workerTerminateTimeout", "onCreateWorker", "onTerminateWorker", "maxWorkers", "isNumber", "isInteger", "TypeError", "validateMaxWorkers", "Math", "max", "cpus", "minWorkers", "validateMinWorkers", "_ensureMinWorkers", "_boundNext", "_next", "bind", "ensureWorkerThreads", "value", "round", "prototype", "exec", "method", "params", "Array", "isArray", "resolver", "defer", "length", "Error", "task", "timeout", "push", "originalTimeout", "promise", "delay", "indexOf", "call", "String", "proxy", "arguments", "pool", "then", "methods", "for<PERSON>ach", "slice", "worker", "_get<PERSON><PERSON><PERSON>", "me", "shift", "pending", "terminated", "_remove<PERSON><PERSON>ker", "i", "busy", "_createWorkerHandler", "releasePort", "debugPort", "_removeWorkerFromList", "resolve", "reject", "terminate", "err", "index", "splice", "force", "removeW<PERSON>ker", "promises", "termPromise", "terminateAndNotify", "always", "all", "stats", "totalWorkers", "busyWorkers", "filter", "idleWorkers", "pendingTasks", "activeTasks", "overriddenParams", "nextAvailableStartingAt", "handler", "parent", "SyntaxError", "_onSuccess", "_onFail", "resolved", "rejected", "_process", "onSuccess", "onFail", "s", "_then", "f", "_resolve", "result", "fn", "_reject", "error", "cancel", "CancellationError", "timer", "setTimeout", "TimeoutError", "clearTimeout", "callback", "res", "message", "stack", "remaining", "results", "p", "constructor", "name", "_arrayLikeToArray", "arr", "len", "arr2", "ownKeys", "e", "r", "t", "keys", "getOwnPropertySymbols", "o", "getOwnPropertyDescriptor", "enumerable", "apply", "_typeof", "Symbol", "iterator", "requireFoolWebpack", "TERMINATE_METHOD_ID", "WorkerThreads", "tryRequireWorkerThreads", "ensureWebWorker", "Worker", "code", "setupBrowserWorker", "isBrowserWorker", "on", "event", "addEventListener", "data", "send", "transfer", "postMessage", "setupWorkerThreadWorker", "workerThreadOptions", "obj", "key", "arg", "input", "hint", "prim", "toPrimitive", "undefined", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "defineProperty", "configurable", "writable", "getOwnPropertyDescriptors", "defineProperties", "_objectSpread", "stdout", "stderr", "isWorkerThread", "kill", "disconnect", "setupProcessWorker", "child_process", "fork", "isChildProcess", "resolveForkOptions", "opts", "processExecArgv", "process", "execArgv", "join", "inspectorActive", "debugBrk", "assign", "concat", "_options", "onError", "id", "processing", "create", "platform", "Blob", "window", "URL", "createObjectURL", "blob", "type", "__dirname", "getDefaultWorker", "setupWorker", "ready", "requestQueue", "response", "_step", "_iterator", "allowArrayLike", "it", "minLen", "n", "toString", "from", "test", "_unsupportedIterableToArray", "F", "done", "_e", "normalCompletion", "didErr", "step", "next", "_e2", "_createForOfIteratorHelper", "request", "dispatchQueuedRequests", "isEvent", "payload", "terminating", "temp", "props", "objectToError", "exitCode", "signalCode", "spawnargs", "spawnfile", "cleaning", "<PERSON><PERSON><PERSON><PERSON>", "lastId", "cleanup", "removeAllListeners", "killed", "cleanExitTimeout", "once", "_tryRequireWorkerThreads", "_setupProcessWorker", "_setupBrowserWorker", "_setupWorkerThreadWorker", "DebugPortAllocator", "ports", "starting", "port", "isNode", "nodeProcess", "versions", "node", "worker_threads", "tryRequireFoolWebpack", "isMainThread", "connected", "Window", "navigator", "hardwareConcurrency", "eval", "Transfer", "exit", "parentPort", "convertError", "getOwnPropertyNames", "reduce", "product", "isPromise", "run", "args", "Function", "cleanupAndExit", "_exit", "currentRequestId", "register", "hasOwnProperty", "onTerminate", "emit", "add", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "workerEmit"], "sourceRoot": ""}