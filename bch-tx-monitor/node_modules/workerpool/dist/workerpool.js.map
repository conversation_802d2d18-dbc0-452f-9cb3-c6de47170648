{"version": 3, "file": "workerpool.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;ACVA,IAAIA,OAAO,GAAGC,mBAAO,CAAC,GAAW,CAAC;AAClC,IAAIC,aAAa,GAAGD,mBAAO,CAAC,GAAiB,CAAC;AAC9C,IAAIE,WAAW,GAAGF,mBAAO,CAAC,GAAe,CAAC;AAC1C,IAAIG,kBAAkB,GAAGH,mBAAO,CAAC,GAAwB,CAAC;AAC1D,IAAII,oBAAoB,GAAG,IAAID,kBAAkB,CAAC,CAAC;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,IAAIA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC7B,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;IAC9B,IAAI,CAACA,MAAM,GAAGA,MAAM,IAAI,IAAI;EAC9B,CAAC,MACI;IACH,IAAI,CAACA,MAAM,GAAG,IAAI;IAClBC,OAAO,GAAGD,MAAM;EAClB;EAEA,IAAI,CAACE,OAAO,GAAG,EAAE,CAAC,CAAE;EACpB,IAAI,CAACC,KAAK,GAAG,EAAE,CAAC,CAAI;;EAEpBF,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,IAAI,CAACG,QAAQ,GAAGC,MAAM,CAACC,MAAM,CAACL,OAAO,CAACG,QAAQ,IAAI,EAAE,CAAC;EACrD,IAAI,CAACG,QAAQ,GAAGF,MAAM,CAACC,MAAM,CAACL,OAAO,CAACM,QAAQ,IAAI,CAAC,CAAC,CAAC;EACrD,IAAI,CAACC,UAAU,GAAGH,MAAM,CAACC,MAAM,CAACL,OAAO,CAACO,UAAU,IAAI,CAAC,CAAC,CAAC;EACzD,IAAI,CAACC,gBAAgB,GAAGJ,MAAM,CAACC,MAAM,CAACL,OAAO,CAACQ,gBAAgB,IAAI,CAAC,CAAC,CAAC;EACrE,IAAI,CAACC,cAAc,GAAIT,OAAO,CAACS,cAAc,IAAI,KAAM;EACvD,IAAI,CAACC,UAAU,GAAGV,OAAO,CAACU,UAAU;EACpC,IAAI,CAACC,UAAU,GAAGX,OAAO,CAACW,UAAU,IAAIX,OAAO,CAACU,UAAU,IAAI,MAAM;EACpE,IAAI,CAACE,YAAY,GAAGZ,OAAO,CAACY,YAAY,IAAIC,QAAQ;EACpD,IAAI,CAACC,sBAAsB,GAAGd,OAAO,CAACc,sBAAsB,IAAI,IAAI;EAEpE,IAAI,CAACC,cAAc,GAAGf,OAAO,CAACe,cAAc,IAAK;IAAA,OAAM,IAAI;EAAA,CAAC;EAC5D,IAAI,CAACC,iBAAiB,GAAGhB,OAAO,CAACgB,iBAAiB,IAAK;IAAA,OAAM,IAAI;EAAA,CAAC;;EAElE;EACA,IAAIhB,OAAO,IAAI,YAAY,IAAIA,OAAO,EAAE;IACtCiB,kBAAkB,CAACjB,OAAO,CAACkB,UAAU,CAAC;IACtC,IAAI,CAACA,UAAU,GAAGlB,OAAO,CAACkB,UAAU;EACtC,CAAC,MACI;IACH,IAAI,CAACA,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACzB,WAAW,CAAC0B,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;EAC5D;EAEA,IAAIrB,OAAO,IAAI,YAAY,IAAIA,OAAO,EAAE;IACtC,IAAGA,OAAO,CAACsB,UAAU,KAAK,KAAK,EAAE;MAC/B,IAAI,CAACA,UAAU,GAAG,IAAI,CAACJ,UAAU;IACnC,CAAC,MAAM;MACLK,kBAAkB,CAACvB,OAAO,CAACsB,UAAU,CAAC;MACtC,IAAI,CAACA,UAAU,GAAGtB,OAAO,CAACsB,UAAU;MACpC,IAAI,CAACJ,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACE,UAAU,EAAE,IAAI,CAACJ,UAAU,CAAC,CAAC,CAAK;IACpE;;IACA,IAAI,CAACM,iBAAiB,CAAC,CAAC;EAC1B;EAEA,IAAI,CAACC,UAAU,GAAG,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC;EAGvC,IAAI,IAAI,CAAChB,UAAU,KAAK,QAAQ,EAAE;IAChCjB,aAAa,CAACkC,mBAAmB,CAAC,CAAC;EACrC;AACF;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA9B,IAAI,CAAC+B,SAAS,CAACC,IAAI,GAAG,UAAUC,MAAM,EAAEC,MAAM,EAAEhC,OAAO,EAAE;EACvD;EACA,IAAIgC,MAAM,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;IACpC,MAAM,IAAIG,SAAS,CAAC,qCAAqC,CAAC;EAC5D;EAEA,IAAI,OAAOJ,MAAM,KAAK,QAAQ,EAAE;IAC9B,IAAIK,QAAQ,GAAG5C,OAAO,CAAC6C,KAAK,CAAC,CAAC;IAE9B,IAAI,IAAI,CAACnC,KAAK,CAACoC,MAAM,IAAI,IAAI,CAAC1B,YAAY,EAAE;MAC1C,MAAM,IAAI2B,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC3B,YAAY,GAAG,UAAU,CAAC;IACxE;;IAEA;IACA,IAAIV,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIsC,IAAI,GAAG;MACTT,MAAM,EAAGA,MAAM;MACfC,MAAM,EAAGA,MAAM;MACfI,QAAQ,EAAEA,QAAQ;MAClBK,OAAO,EAAE,IAAI;MACbzC,OAAO,EAAEA;IACX,CAAC;IACDE,KAAK,CAACwC,IAAI,CAACF,IAAI,CAAC;;IAEhB;IACA;IACA,IAAIG,eAAe,GAAGP,QAAQ,CAACQ,OAAO,CAACH,OAAO;IAC9CL,QAAQ,CAACQ,OAAO,CAACH,OAAO,GAAG,SAASA,OAAOA,CAAEI,KAAK,EAAE;MAClD,IAAI3C,KAAK,CAAC4C,OAAO,CAACN,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QAC9B;QACAA,IAAI,CAACC,OAAO,GAAGI,KAAK;QACpB,OAAOT,QAAQ,CAACQ,OAAO;MACzB,CAAC,MACI;QACH;QACA,OAAOD,eAAe,CAACI,IAAI,CAACX,QAAQ,CAACQ,OAAO,EAAEC,KAAK,CAAC;MACtD;IACF,CAAC;;IAED;IACA,IAAI,CAACnB,KAAK,CAAC,CAAC;IAEZ,OAAOU,QAAQ,CAACQ,OAAO;EACzB,CAAC,MACI,IAAI,OAAOb,MAAM,KAAK,UAAU,EAAE;IACrC;IACA,OAAO,IAAI,CAACD,IAAI,CAAC,KAAK,EAAE,CAACkB,MAAM,CAACjB,MAAM,CAAC,EAAEC,MAAM,CAAC,CAAC;EACnD,CAAC,MACI;IACH,MAAM,IAAIG,SAAS,CAAC,kDAAkD,CAAC;EACzE;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACArC,IAAI,CAAC+B,SAAS,CAACoB,KAAK,GAAG,YAAY;EACjC,IAAIC,SAAS,CAACZ,MAAM,GAAG,CAAC,EAAE;IACxB,MAAM,IAAIC,KAAK,CAAC,uBAAuB,CAAC;EAC1C;EAEA,IAAIY,IAAI,GAAG,IAAI;EACf,OAAO,IAAI,CAACrB,IAAI,CAAC,SAAS,CAAC,CACtBsB,IAAI,CAAC,UAAUC,OAAO,EAAE;IACvB,IAAIJ,KAAK,GAAG,CAAC,CAAC;IAEdI,OAAO,CAACC,OAAO,CAAC,UAAUvB,MAAM,EAAE;MAChCkB,KAAK,CAAClB,MAAM,CAAC,GAAG,YAAY;QAC1B,OAAOoB,IAAI,CAACrB,IAAI,CAACC,MAAM,EAAEE,KAAK,CAACJ,SAAS,CAAC0B,KAAK,CAACR,IAAI,CAACG,SAAS,CAAC,CAAC;MACjE,CAAC;IACH,CAAC,CAAC;IAEF,OAAOD,KAAK;EACd,CAAC,CAAC;AACR,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACAnD,IAAI,CAAC+B,SAAS,CAACH,KAAK,GAAG,YAAY;EACjC,IAAI,IAAI,CAACxB,KAAK,CAACoC,MAAM,GAAG,CAAC,EAAE;IACzB;;IAEA;IACA,IAAIkB,MAAM,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAC9B,IAAID,MAAM,EAAE;MACV;MACA,IAAIE,EAAE,GAAG,IAAI;MACb,IAAIlB,IAAI,GAAG,IAAI,CAACtC,KAAK,CAACyD,KAAK,CAAC,CAAC;;MAE7B;MACA,IAAInB,IAAI,CAACJ,QAAQ,CAACQ,OAAO,CAACgB,OAAO,EAAE;QACjC;QACA,IAAIhB,OAAO,GAAGY,MAAM,CAAC1B,IAAI,CAACU,IAAI,CAACT,MAAM,EAAES,IAAI,CAACR,MAAM,EAAEQ,IAAI,CAACJ,QAAQ,EAAEI,IAAI,CAACxC,OAAO,CAAC,CAC7EoD,IAAI,CAACM,EAAE,CAACjC,UAAU,CAAC,SACd,CAAC,YAAY;UACjB;UACA,IAAI+B,MAAM,CAACK,UAAU,EAAE;YACrB,OAAOH,EAAE,CAACI,aAAa,CAACN,MAAM,CAAC;UACjC;QACF,CAAC,CAAC,CAACJ,IAAI,CAAC,YAAW;UACjBM,EAAE,CAAChC,KAAK,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC;;QAEJ;QACA,IAAI,OAAOc,IAAI,CAACC,OAAO,KAAK,QAAQ,EAAE;UACpCG,OAAO,CAACH,OAAO,CAACD,IAAI,CAACC,OAAO,CAAC;QAC/B;MACF,CAAC,MAAM;QACL;QACAiB,EAAE,CAAChC,KAAK,CAAC,CAAC;MACZ;IACF;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA5B,IAAI,CAAC+B,SAAS,CAAC4B,UAAU,GAAG,YAAW;EACrC;EACA,IAAIxD,OAAO,GAAG,IAAI,CAACA,OAAO;EAC1B,KAAK,IAAI8D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9D,OAAO,CAACqC,MAAM,EAAEyB,CAAC,EAAE,EAAE;IACvC,IAAIP,MAAM,GAAGvD,OAAO,CAAC8D,CAAC,CAAC;IACvB,IAAIP,MAAM,CAACQ,IAAI,CAAC,CAAC,KAAK,KAAK,EAAE;MAC3B,OAAOR,MAAM;IACf;EACF;EAEA,IAAIvD,OAAO,CAACqC,MAAM,GAAG,IAAI,CAACpB,UAAU,EAAE;IACpC;IACAsC,MAAM,GAAG,IAAI,CAACS,oBAAoB,CAAC,CAAC;IACpChE,OAAO,CAACyC,IAAI,CAACc,MAAM,CAAC;IACpB,OAAOA,MAAM;EACf;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA1D,IAAI,CAAC+B,SAAS,CAACiC,aAAa,GAAG,UAASN,MAAM,EAAE;EAC9C,IAAIE,EAAE,GAAG,IAAI;EAEb7D,oBAAoB,CAACqE,WAAW,CAACV,MAAM,CAACW,SAAS,CAAC;EAClD;EACA,IAAI,CAACC,qBAAqB,CAACZ,MAAM,CAAC;EAClC;EACA,IAAI,CAAChC,iBAAiB,CAAC,CAAC;EACxB;EACA,OAAO,IAAIhC,OAAO,CAAC,UAAS6E,OAAO,EAAEC,MAAM,EAAE;IAC3Cd,MAAM,CAACe,SAAS,CAAC,KAAK,EAAE,UAASC,GAAG,EAAE;MACpCd,EAAE,CAAC1C,iBAAiB,CAAC;QACnBb,QAAQ,EAAEqD,MAAM,CAACrD,QAAQ;QACzBG,QAAQ,EAAEkD,MAAM,CAAClD,QAAQ;QACzBE,gBAAgB,EAAEgD,MAAM,CAAChD,gBAAgB;QACzCT,MAAM,EAAEyD,MAAM,CAACzD;MACjB,CAAC,CAAC;MACF,IAAIyE,GAAG,EAAE;QACPF,MAAM,CAACE,GAAG,CAAC;MACb,CAAC,MAAM;QACLH,OAAO,CAACb,MAAM,CAAC;MACjB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA1D,IAAI,CAAC+B,SAAS,CAACuC,qBAAqB,GAAG,UAASZ,MAAM,EAAE;EACtD;EACA,IAAIiB,KAAK,GAAG,IAAI,CAACxE,OAAO,CAAC6C,OAAO,CAACU,MAAM,CAAC;EACxC,IAAIiB,KAAK,KAAK,CAAC,CAAC,EAAE;IAChB,IAAI,CAACxE,OAAO,CAACyE,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;EAC/B;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA3E,IAAI,CAAC+B,SAAS,CAAC0C,SAAS,GAAG,UAAUI,KAAK,EAAElC,OAAO,EAAE;EACnD,IAAIiB,EAAE,GAAG,IAAI;;EAEb;EACA,IAAI,CAACxD,KAAK,CAACoD,OAAO,CAAC,UAAUd,IAAI,EAAE;IACjCA,IAAI,CAACJ,QAAQ,CAACkC,MAAM,CAAC,IAAI/B,KAAK,CAAC,iBAAiB,CAAC,CAAC;EACpD,CAAC,CAAC;EACF,IAAI,CAACrC,KAAK,CAACoC,MAAM,GAAG,CAAC;EAErB,IAAIsC,CAAC,GAAG,SAAJA,CAACA,CAAapB,MAAM,EAAE;IACxB3D,oBAAoB,CAACqE,WAAW,CAACV,MAAM,CAACW,SAAS,CAAC;IAClD,IAAI,CAACC,qBAAqB,CAACZ,MAAM,CAAC;EACpC,CAAC;EACD,IAAIqB,YAAY,GAAGD,CAAC,CAACjD,IAAI,CAAC,IAAI,CAAC;EAE/B,IAAImD,QAAQ,GAAG,EAAE;EACjB,IAAI7E,OAAO,GAAG,IAAI,CAACA,OAAO,CAACsD,KAAK,CAAC,CAAC;EAClCtD,OAAO,CAACqD,OAAO,CAAC,UAAUE,MAAM,EAAE;IAChC,IAAIuB,WAAW,GAAGvB,MAAM,CAACwB,kBAAkB,CAACL,KAAK,EAAElC,OAAO,CAAC,CACxDW,IAAI,CAACyB,YAAY,CAAC,CAClBI,MAAM,CAAC,YAAW;MACjBvB,EAAE,CAAC1C,iBAAiB,CAAC;QACnBb,QAAQ,EAAEqD,MAAM,CAACrD,QAAQ;QACzBG,QAAQ,EAAEkD,MAAM,CAAClD,QAAQ;QACzBE,gBAAgB,EAAEgD,MAAM,CAAChD,gBAAgB;QACzCT,MAAM,EAAEyD,MAAM,CAACzD;MACjB,CAAC,CAAC;IACJ,CAAC,CAAC;IACJ+E,QAAQ,CAACpC,IAAI,CAACqC,WAAW,CAAC;EAC5B,CAAC,CAAC;EACF,OAAOvF,OAAO,CAAC0F,GAAG,CAACJ,QAAQ,CAAC;AAC9B,CAAC;;AAED;AACA;AACA;AACA;AACAhF,IAAI,CAAC+B,SAAS,CAACsD,KAAK,GAAG,YAAY;EACjC,IAAIC,YAAY,GAAG,IAAI,CAACnF,OAAO,CAACqC,MAAM;EACtC,IAAI+C,WAAW,GAAG,IAAI,CAACpF,OAAO,CAACqF,MAAM,CAAC,UAAU9B,MAAM,EAAE;IACtD,OAAOA,MAAM,CAACQ,IAAI,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC1B,MAAM;EAET,OAAO;IACL8C,YAAY,EAAGA,YAAY;IAC3BC,WAAW,EAAIA,WAAW;IAC1BE,WAAW,EAAIH,YAAY,GAAGC,WAAW;IAEzCG,YAAY,EAAG,IAAI,CAACtF,KAAK,CAACoC,MAAM;IAChCmD,WAAW,EAAIJ;EACjB,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACAvF,IAAI,CAAC+B,SAAS,CAACL,iBAAiB,GAAG,YAAW;EAC5C,IAAI,IAAI,CAACF,UAAU,EAAE;IACnB,KAAI,IAAIyC,CAAC,GAAG,IAAI,CAAC9D,OAAO,CAACqC,MAAM,EAAEyB,CAAC,GAAG,IAAI,CAACzC,UAAU,EAAEyC,CAAC,EAAE,EAAE;MACzD,IAAI,CAAC9D,OAAO,CAACyC,IAAI,CAAC,IAAI,CAACuB,oBAAoB,CAAC,CAAC,CAAC;IAChD;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACAnE,IAAI,CAAC+B,SAAS,CAACoC,oBAAoB,GAAG,YAAY;EAChD,IAAMyB,gBAAgB,GAAG,IAAI,CAAC3E,cAAc,CAAC;IAC3CZ,QAAQ,EAAE,IAAI,CAACA,QAAQ;IACvBG,QAAQ,EAAE,IAAI,CAACA,QAAQ;IACvBC,UAAU,EAAE,IAAI,CAACA,UAAU;IAC3BC,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;IACvCT,MAAM,EAAE,IAAI,CAACA;EACf,CAAC,CAAC,IAAI,CAAC,CAAC;EAER,OAAO,IAAIL,aAAa,CAACgG,gBAAgB,CAAC3F,MAAM,IAAI,IAAI,CAACA,MAAM,EAAE;IAC/DI,QAAQ,EAAEuF,gBAAgB,CAACvF,QAAQ,IAAI,IAAI,CAACA,QAAQ;IACpDG,QAAQ,EAAEoF,gBAAgB,CAACpF,QAAQ,IAAI,IAAI,CAACA,QAAQ;IACpDC,UAAU,EAAEmF,gBAAgB,CAACnF,UAAU,IAAI,IAAI,CAACA,UAAU;IAC1DC,gBAAgB,EAAEkF,gBAAgB,CAAClF,gBAAgB,IAAI,IAAI,CAACA,gBAAgB;IAC5E2D,SAAS,EAAEtE,oBAAoB,CAAC8F,uBAAuB,CAAC,IAAI,CAAClF,cAAc,CAAC;IAC5EE,UAAU,EAAE,IAAI,CAACA,UAAU;IAC3BG,sBAAsB,EAAE,IAAI,CAACA;EAC/B,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,SAASG,kBAAkBA,CAACC,UAAU,EAAE;EACtC,IAAI,CAAC0E,QAAQ,CAAC1E,UAAU,CAAC,IAAI,CAAC2E,SAAS,CAAC3E,UAAU,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;IACrE,MAAM,IAAIiB,SAAS,CAAC,kDAAkD,CAAC;EACzE;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASZ,kBAAkBA,CAACD,UAAU,EAAE;EACtC,IAAI,CAACsE,QAAQ,CAACtE,UAAU,CAAC,IAAI,CAACuE,SAAS,CAACvE,UAAU,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;IACrE,MAAM,IAAIa,SAAS,CAAC,kDAAkD,CAAC;EACzE;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASyD,QAAQA,CAACE,KAAK,EAAE;EACvB,OAAO,OAAOA,KAAK,KAAK,QAAQ;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASD,SAASA,CAACC,KAAK,EAAE;EACxB,OAAO3E,IAAI,CAAC4E,KAAK,CAACD,KAAK,CAAC,IAAIA,KAAK;AACnC;AAEAE,MAAM,CAACC,OAAO,GAAGnG,IAAI;;;;;;;;ACncR;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASN,OAAOA,CAAC0G,OAAO,EAAEC,MAAM,EAAE;EAChC,IAAIzC,EAAE,GAAG,IAAI;EAEb,IAAI,EAAE,IAAI,YAAYlE,OAAO,CAAC,EAAE;IAC9B,MAAM,IAAI4G,WAAW,CAAC,kDAAkD,CAAC;EAC3E;EAEA,IAAI,OAAOF,OAAO,KAAK,UAAU,EAAE;IACjC,MAAM,IAAIE,WAAW,CAAC,qDAAqD,CAAC;EAC9E;EAEA,IAAIC,UAAU,GAAG,EAAE;EACnB,IAAIC,OAAO,GAAG,EAAE;;EAEhB;EACA,IAAI,CAACC,QAAQ,GAAG,KAAK;EACrB,IAAI,CAACC,QAAQ,GAAG,KAAK;EACrB,IAAI,CAAC5C,OAAO,GAAG,IAAI;;EAEnB;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAI6C,QAAQ,GAAG,SAAAA,SAAUC,SAAS,EAAEC,MAAM,EAAE;IAC1CN,UAAU,CAAC3D,IAAI,CAACgE,SAAS,CAAC;IAC1BJ,OAAO,CAAC5D,IAAI,CAACiE,MAAM,CAAC;EACtB,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;EACE,IAAI,CAACvD,IAAI,GAAG,UAAUsD,SAAS,EAAEC,MAAM,EAAE;IACvC,OAAO,IAAInH,OAAO,CAAC,UAAU6E,OAAO,EAAEC,MAAM,EAAE;MAC5C,IAAIsC,CAAC,GAAGF,SAAS,GAAGG,KAAK,CAACH,SAAS,EAAErC,OAAO,EAAEC,MAAM,CAAC,GAAGD,OAAO;MAC/D,IAAIO,CAAC,GAAG+B,MAAM,GAAME,KAAK,CAACF,MAAM,EAAKtC,OAAO,EAAEC,MAAM,CAAC,GAAGA,MAAM;MAE9DmC,QAAQ,CAACG,CAAC,EAAEhC,CAAC,CAAC;IAChB,CAAC,EAAElB,EAAE,CAAC;EACR,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,IAAIoD,SAAQ,GAAG,SAAAA,SAAUC,MAAM,EAAE;IAC/B;IACArD,EAAE,CAAC6C,QAAQ,GAAG,IAAI;IAClB7C,EAAE,CAAC8C,QAAQ,GAAG,KAAK;IACnB9C,EAAE,CAACE,OAAO,GAAG,KAAK;IAElByC,UAAU,CAAC/C,OAAO,CAAC,UAAU0D,EAAE,EAAE;MAC/BA,EAAE,CAACD,MAAM,CAAC;IACZ,CAAC,CAAC;IAEFN,QAAQ,GAAG,SAAAA,SAAUC,SAAS,EAAEC,MAAM,EAAE;MACtCD,SAAS,CAACK,MAAM,CAAC;IACnB,CAAC;IAEDD,SAAQ,GAAGG,QAAO,GAAG,SAAAA,QAAA,EAAY,CAAE,CAAC;IAEpC,OAAOvD,EAAE;EACX,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,IAAIuD,QAAO,GAAG,SAAAA,QAAUC,KAAK,EAAE;IAC7B;IACAxD,EAAE,CAAC6C,QAAQ,GAAG,KAAK;IACnB7C,EAAE,CAAC8C,QAAQ,GAAG,IAAI;IAClB9C,EAAE,CAACE,OAAO,GAAG,KAAK;IAElB0C,OAAO,CAAChD,OAAO,CAAC,UAAU0D,EAAE,EAAE;MAC5BA,EAAE,CAACE,KAAK,CAAC;IACX,CAAC,CAAC;IAEFT,QAAQ,GAAG,SAAAA,SAAUC,SAAS,EAAEC,MAAM,EAAE;MACtCA,MAAM,CAACO,KAAK,CAAC;IACf,CAAC;IAEDJ,SAAQ,GAAGG,QAAO,GAAG,SAAAA,QAAA,EAAY,CAAE,CAAC;IAEpC,OAAOvD,EAAE;EACX,CAAC;;EAED;AACF;AACA;AACA;EACE,IAAI,CAACyD,MAAM,GAAG,YAAY;IACxB,IAAIhB,MAAM,EAAE;MACVA,MAAM,CAACgB,MAAM,CAAC,CAAC;IACjB,CAAC,MACI;MACHF,QAAO,CAAC,IAAIG,iBAAiB,CAAC,CAAC,CAAC;IAClC;IAEA,OAAO1D,EAAE;EACX,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAI,CAACjB,OAAO,GAAG,UAAUI,KAAK,EAAE;IAC9B,IAAIsD,MAAM,EAAE;MACVA,MAAM,CAAC1D,OAAO,CAACI,KAAK,CAAC;IACvB,CAAC,MACI;MACH,IAAIwE,KAAK,GAAGC,UAAU,CAAC,YAAY;QACjCL,QAAO,CAAC,IAAIM,YAAY,CAAC,0BAA0B,GAAG1E,KAAK,GAAG,KAAK,CAAC,CAAC;MACvE,CAAC,EAAEA,KAAK,CAAC;MAETa,EAAE,CAACuB,MAAM,CAAC,YAAY;QACpBuC,YAAY,CAACH,KAAK,CAAC;MACrB,CAAC,CAAC;IACJ;IAEA,OAAO3D,EAAE;EACX,CAAC;;EAED;EACAwC,OAAO,CAAC,UAAUa,MAAM,EAAE;IACxBD,SAAQ,CAACC,MAAM,CAAC;EAClB,CAAC,EAAE,UAAUG,KAAK,EAAE;IAClBD,QAAO,CAACC,KAAK,CAAC;EAChB,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASL,KAAKA,CAACY,QAAQ,EAAEpD,OAAO,EAAEC,MAAM,EAAE;EACxC,OAAO,UAAUyC,MAAM,EAAE;IACvB,IAAI;MACF,IAAIW,GAAG,GAAGD,QAAQ,CAACV,MAAM,CAAC;MAC1B,IAAIW,GAAG,IAAI,OAAOA,GAAG,CAACtE,IAAI,KAAK,UAAU,IAAI,OAAOsE,GAAG,CAAC,OAAO,CAAC,KAAK,UAAU,EAAE;QAC/E;QACAA,GAAG,CAACtE,IAAI,CAACiB,OAAO,EAAEC,MAAM,CAAC;MAC3B,CAAC,MACI;QACHD,OAAO,CAACqD,GAAG,CAAC;MACd;IACF,CAAC,CACD,OAAOR,KAAK,EAAE;MACZ5C,MAAM,CAAC4C,KAAK,CAAC;IACf;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA1H,OAAO,CAACqC,SAAS,CAAC,OAAO,CAAC,GAAG,UAAU8E,MAAM,EAAE;EAC7C,OAAO,IAAI,CAACvD,IAAI,CAAC,IAAI,EAAEuD,MAAM,CAAC;AAChC,CAAC;;AAED;AACA;;AAEA;AACA;AACA;AACA;AACA;AACAnH,OAAO,CAACqC,SAAS,CAACoD,MAAM,GAAG,UAAU+B,EAAE,EAAE;EACvC,OAAO,IAAI,CAAC5D,IAAI,CAAC4D,EAAE,EAAEA,EAAE,CAAC;AAC1B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAxH,OAAO,CAAC0F,GAAG,GAAG,UAAUJ,QAAQ,EAAC;EAC/B,OAAO,IAAItF,OAAO,CAAC,UAAU6E,OAAO,EAAEC,MAAM,EAAE;IAC5C,IAAIqD,SAAS,GAAG7C,QAAQ,CAACxC,MAAM;MAC3BsF,OAAO,GAAG,EAAE;IAEhB,IAAID,SAAS,EAAE;MACb7C,QAAQ,CAACxB,OAAO,CAAC,UAAUuE,CAAC,EAAE9D,CAAC,EAAE;QAC/B8D,CAAC,CAACzE,IAAI,CAAC,UAAU2D,MAAM,EAAE;UACvBa,OAAO,CAAC7D,CAAC,CAAC,GAAGgD,MAAM;UACnBY,SAAS,EAAE;UACX,IAAIA,SAAS,IAAI,CAAC,EAAE;YAClBtD,OAAO,CAACuD,OAAO,CAAC;UAClB;QACF,CAAC,EAAE,UAAUV,KAAK,EAAE;UAClBS,SAAS,GAAG,CAAC;UACbrD,MAAM,CAAC4C,KAAK,CAAC;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MACI;MACH7C,OAAO,CAACuD,OAAO,CAAC;IAClB;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACApI,OAAO,CAAC6C,KAAK,GAAG,YAAY;EAC1B,IAAID,QAAQ,GAAG,CAAC,CAAC;EAEjBA,QAAQ,CAACQ,OAAO,GAAG,IAAIpD,OAAO,CAAC,UAAU6E,OAAO,EAAEC,MAAM,EAAE;IACxDlC,QAAQ,CAACiC,OAAO,GAAGA,OAAO;IAC1BjC,QAAQ,CAACkC,MAAM,GAAGA,MAAM;EAC1B,CAAC,CAAC;EAEF,OAAOlC,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,SAASgF,iBAAiBA,CAACU,OAAO,EAAE;EAClC,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAI,mBAAmB;EAC7C,IAAI,CAACC,KAAK,GAAI,IAAIxF,KAAK,CAAC,CAAC,CAAEwF,KAAK;AAClC;AAEAX,iBAAiB,CAACvF,SAAS,GAAG,IAAIU,KAAK,CAAC,CAAC;AACzC6E,iBAAiB,CAACvF,SAAS,CAACmG,WAAW,GAAGzF,KAAK;AAC/C6E,iBAAiB,CAACvF,SAAS,CAACoG,IAAI,GAAG,mBAAmB;AAEtDzI,OAAO,CAAC4H,iBAAiB,GAAGA,iBAAiB;;AAG7C;AACA;AACA;AACA;AACA;AACA,SAASG,YAAYA,CAACO,OAAO,EAAE;EAC7B,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAI,kBAAkB;EAC5C,IAAI,CAACC,KAAK,GAAI,IAAIxF,KAAK,CAAC,CAAC,CAAEwF,KAAK;AAClC;AAEAR,YAAY,CAAC1F,SAAS,GAAG,IAAIU,KAAK,CAAC,CAAC;AACpCgF,YAAY,CAAC1F,SAAS,CAACmG,WAAW,GAAGzF,KAAK;AAC1CgF,YAAY,CAAC1F,SAAS,CAACoG,IAAI,GAAG,cAAc;AAE5CzI,OAAO,CAAC+H,YAAY,GAAGA,YAAY;AAGnCvB,MAAM,CAACC,OAAO,GAAGzG,OAAO;;;;;;;;ACtRX;;AAAA,SAAA0I,2BAAAC,CAAA,EAAAC,cAAA,QAAAC,EAAA,UAAAC,MAAA,oBAAAH,CAAA,CAAAG,MAAA,CAAAC,QAAA,KAAAJ,CAAA,qBAAAE,EAAA,QAAApG,KAAA,CAAAC,OAAA,CAAAiG,CAAA,MAAAE,EAAA,GAAAG,2BAAA,CAAAL,CAAA,MAAAC,cAAA,IAAAD,CAAA,WAAAA,CAAA,CAAA7F,MAAA,qBAAA+F,EAAA,EAAAF,CAAA,GAAAE,EAAA,MAAAtE,CAAA,UAAA0E,CAAA,YAAAA,EAAA,eAAA7B,CAAA,EAAA6B,CAAA,EAAAC,CAAA,WAAAA,EAAA,QAAA3E,CAAA,IAAAoE,CAAA,CAAA7F,MAAA,WAAAqG,IAAA,mBAAAA,IAAA,SAAA7C,KAAA,EAAAqC,CAAA,CAAApE,CAAA,UAAA6E,CAAA,WAAAA,EAAAC,EAAA,UAAAA,EAAA,KAAAjE,CAAA,EAAA6D,CAAA,gBAAAtG,SAAA,iJAAA2G,gBAAA,SAAAC,MAAA,UAAAvE,GAAA,WAAAoC,CAAA,WAAAA,EAAA,IAAAyB,EAAA,GAAAA,EAAA,CAAAtF,IAAA,CAAAoF,CAAA,MAAAO,CAAA,WAAAA,EAAA,QAAAM,IAAA,GAAAX,EAAA,CAAAY,IAAA,IAAAH,gBAAA,GAAAE,IAAA,CAAAL,IAAA,SAAAK,IAAA,KAAAJ,CAAA,WAAAA,EAAAM,GAAA,IAAAH,MAAA,SAAAvE,GAAA,GAAA0E,GAAA,KAAAtE,CAAA,WAAAA,EAAA,eAAAkE,gBAAA,IAAAT,EAAA,oBAAAA,EAAA,8BAAAU,MAAA,QAAAvE,GAAA;AAAA,SAAAgE,4BAAAL,CAAA,EAAAgB,MAAA,SAAAhB,CAAA,qBAAAA,CAAA,sBAAAiB,iBAAA,CAAAjB,CAAA,EAAAgB,MAAA,OAAAT,CAAA,GAAAtI,MAAA,CAAAyB,SAAA,CAAAwH,QAAA,CAAAtG,IAAA,CAAAoF,CAAA,EAAA5E,KAAA,aAAAmF,CAAA,iBAAAP,CAAA,CAAAH,WAAA,EAAAU,CAAA,GAAAP,CAAA,CAAAH,WAAA,CAAAC,IAAA,MAAAS,CAAA,cAAAA,CAAA,mBAAAzG,KAAA,CAAAqH,IAAA,CAAAnB,CAAA,OAAAO,CAAA,+DAAAa,IAAA,CAAAb,CAAA,UAAAU,iBAAA,CAAAjB,CAAA,EAAAgB,MAAA;AAAA,SAAAC,kBAAAI,GAAA,EAAAC,GAAA,QAAAA,GAAA,YAAAA,GAAA,GAAAD,GAAA,CAAAlH,MAAA,EAAAmH,GAAA,GAAAD,GAAA,CAAAlH,MAAA,WAAAyB,CAAA,MAAA2F,IAAA,OAAAzH,KAAA,CAAAwH,GAAA,GAAA1F,CAAA,GAAA0F,GAAA,EAAA1F,CAAA,IAAA2F,IAAA,CAAA3F,CAAA,IAAAyF,GAAA,CAAAzF,CAAA,UAAA2F,IAAA;AAAA,SAAAC,QAAAf,CAAA,EAAAgB,CAAA,QAAAC,CAAA,GAAAzJ,MAAA,CAAA0J,IAAA,CAAAlB,CAAA,OAAAxI,MAAA,CAAA2J,qBAAA,QAAA5B,CAAA,GAAA/H,MAAA,CAAA2J,qBAAA,CAAAnB,CAAA,GAAAgB,CAAA,KAAAzB,CAAA,GAAAA,CAAA,CAAA7C,MAAA,WAAAsE,CAAA,WAAAxJ,MAAA,CAAA4J,wBAAA,CAAApB,CAAA,EAAAgB,CAAA,EAAAK,UAAA,OAAAJ,CAAA,CAAAnH,IAAA,CAAAwH,KAAA,CAAAL,CAAA,EAAA1B,CAAA,YAAA0B,CAAA;AAAA,SAAAM,cAAAvB,CAAA,aAAAgB,CAAA,MAAAA,CAAA,GAAA1G,SAAA,CAAAZ,MAAA,EAAAsH,CAAA,UAAAC,CAAA,WAAA3G,SAAA,CAAA0G,CAAA,IAAA1G,SAAA,CAAA0G,CAAA,QAAAA,CAAA,OAAAD,OAAA,CAAAvJ,MAAA,CAAAyJ,CAAA,OAAAvG,OAAA,WAAAsG,CAAA,IAAAQ,eAAA,CAAAxB,CAAA,EAAAgB,CAAA,EAAAC,CAAA,CAAAD,CAAA,SAAAxJ,MAAA,CAAAiK,yBAAA,GAAAjK,MAAA,CAAAkK,gBAAA,CAAA1B,CAAA,EAAAxI,MAAA,CAAAiK,yBAAA,CAAAR,CAAA,KAAAF,OAAA,CAAAvJ,MAAA,CAAAyJ,CAAA,GAAAvG,OAAA,WAAAsG,CAAA,IAAAxJ,MAAA,CAAAmK,cAAA,CAAA3B,CAAA,EAAAgB,CAAA,EAAAxJ,MAAA,CAAA4J,wBAAA,CAAAH,CAAA,EAAAD,CAAA,iBAAAhB,CAAA;AAAA,SAAAwB,gBAAAI,GAAA,EAAAC,GAAA,EAAA3E,KAAA,IAAA2E,GAAA,GAAAC,cAAA,CAAAD,GAAA,OAAAA,GAAA,IAAAD,GAAA,IAAApK,MAAA,CAAAmK,cAAA,CAAAC,GAAA,EAAAC,GAAA,IAAA3E,KAAA,EAAAA,KAAA,EAAAmE,UAAA,QAAAU,YAAA,QAAAC,QAAA,oBAAAJ,GAAA,CAAAC,GAAA,IAAA3E,KAAA,WAAA0E,GAAA;AAAA,SAAAE,eAAAG,GAAA,QAAAJ,GAAA,GAAAK,YAAA,CAAAD,GAAA,oBAAAE,OAAA,CAAAN,GAAA,iBAAAA,GAAA,GAAAzH,MAAA,CAAAyH,GAAA;AAAA,SAAAK,aAAAE,KAAA,EAAAC,IAAA,QAAAF,OAAA,CAAAC,KAAA,kBAAAA,KAAA,kBAAAA,KAAA,MAAAE,IAAA,GAAAF,KAAA,CAAA1C,MAAA,CAAA6C,WAAA,OAAAD,IAAA,KAAAE,SAAA,QAAA1D,GAAA,GAAAwD,IAAA,CAAAnI,IAAA,CAAAiI,KAAA,EAAAC,IAAA,oBAAAF,OAAA,CAAArD,GAAA,uBAAAA,GAAA,YAAAvF,SAAA,4DAAA8I,IAAA,gBAAAjI,MAAA,GAAAqI,MAAA,EAAAL,KAAA;AAAA,SAAAD,QAAA5C,CAAA,sCAAA4C,OAAA,wBAAAzC,MAAA,uBAAAA,MAAA,CAAAC,QAAA,aAAAJ,CAAA,kBAAAA,CAAA,gBAAAA,CAAA,WAAAA,CAAA,yBAAAG,MAAA,IAAAH,CAAA,CAAAH,WAAA,KAAAM,MAAA,IAAAH,CAAA,KAAAG,MAAA,CAAAzG,SAAA,qBAAAsG,CAAA,KAAA4C,OAAA,CAAA5C,CAAA;AAEb,IAAI3I,OAAO,GAAGC,mBAAO,CAAC,GAAW,CAAC;AAClC,IAAIE,WAAW,GAAGF,mBAAO,CAAC,GAAe,CAAC;AAC1C,IAAI6L,kBAAkB,GAAG7L,mBAAO,CAAC,GAAsB,CAAC;;AAExD;AACA;AACA;AACA;AACA,IAAI8L,mBAAmB,GAAG,0BAA0B;AAEpD,SAAS3J,mBAAmBA,CAAA,EAAG;EAC7B,IAAI4J,aAAa,GAAGC,uBAAuB,CAAC,CAAC;EAC7C,IAAI,CAACD,aAAa,EAAE;IAClB,MAAM,IAAIjJ,KAAK,CAAC,+EAA+E,CAAC;EAClG;EAEA,OAAOiJ,aAAa;AACtB;;AAEA;AACA,SAASE,eAAeA,CAAA,EAAG;EACzB;EACA,IAAI,OAAOC,MAAM,KAAK,UAAU,KAAK,QAAOA,MAAM,iCAAAZ,OAAA,CAANY,MAAM,OAAK,QAAQ,IAAI,OAAOA,MAAM,CAAC9J,SAAS,CAACmG,WAAW,KAAK,UAAU,CAAC,EAAE;IACtH,MAAM,IAAIzF,KAAK,CAAC,uCAAuC,CAAC;EAC1D;AACF;AAEA,SAASkJ,uBAAuBA,CAAA,EAAG;EACjC,IAAI;IACF,OAAOH,kBAAkB,CAAC,gBAAgB,CAAC;EAC7C,CAAC,CAAC,OAAMpE,KAAK,EAAE;IACb,IAAI6D,OAAA,CAAO7D,KAAK,MAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,CAAC0E,IAAI,KAAK,kBAAkB,EAAE;MACpF;MACA,OAAO,IAAI;IACb,CAAC,MAAM;MACL,MAAM1E,KAAK;IACb;EACF;AACF;;AAEA;AACA,SAAS2E,gBAAgBA,CAAA,EAAG;EAC1B,IAAIlM,WAAW,CAACmM,QAAQ,KAAK,SAAS,EAAE;IACtC;IACA,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;MAC/B,MAAM,IAAIxJ,KAAK,CAAC,mCAAmC,CAAC;IACtD;IACA,IAAI,CAACyJ,MAAM,CAACC,GAAG,IAAI,OAAOD,MAAM,CAACC,GAAG,CAACC,eAAe,KAAK,UAAU,EAAE;MACnE,MAAM,IAAI3J,KAAK,CAAC,kDAAkD,CAAC;IACrE;;IAEA;IACA,IAAI4J,IAAI,GAAG,IAAIJ,IAAI,CAAC,CAACtM,mBAAO,CAAC,GAA4B,CAAC,CAAC,EAAE;MAAC2M,IAAI,EAAE;IAAiB,CAAC,CAAC;IACvF,OAAOJ,MAAM,CAACC,GAAG,CAACC,eAAe,CAACC,IAAI,CAAC;EACzC,CAAC,MACI;IACH;IACA,OAAOE,SAAS,GAAG,YAAY;EACjC;AACF;AAEA,SAASC,WAAWA,CAACvM,MAAM,EAAEC,OAAO,EAAE;EACpC,IAAIA,OAAO,CAACW,UAAU,KAAK,KAAK,EAAE;IAAE;IAClC+K,eAAe,CAAC,CAAC;IACjB,OAAOa,kBAAkB,CAACxM,MAAM,EAAEC,OAAO,CAACO,UAAU,EAAEoL,MAAM,CAAC;EAC/D,CAAC,MAAM,IAAI3L,OAAO,CAACW,UAAU,KAAK,QAAQ,EAAE;IAAE;IAC5C6K,aAAa,GAAG5J,mBAAmB,CAAC,CAAC;IACrC,OAAO4K,uBAAuB,CAACzM,MAAM,EAAEyL,aAAa,EAAExL,OAAO,CAACQ,gBAAgB,CAAC;EACjF,CAAC,MAAM,IAAIR,OAAO,CAACW,UAAU,KAAK,SAAS,IAAI,CAACX,OAAO,CAACW,UAAU,EAAE;IAAE;IACpE,OAAO8L,kBAAkB,CAAC1M,MAAM,EAAE2M,kBAAkB,CAAC1M,OAAO,CAAC,EAAEsL,kBAAkB,CAAC,eAAe,CAAC,CAAC;EACrG,CAAC,MAAM;IAAE;IACP,IAAI3L,WAAW,CAACmM,QAAQ,KAAK,SAAS,EAAE;MACtCJ,eAAe,CAAC,CAAC;MACjB,OAAOa,kBAAkB,CAACxM,MAAM,EAAEC,OAAO,CAACO,UAAU,EAAEoL,MAAM,CAAC;IAC/D,CAAC,MACI;MAAE;MACL,IAAIH,aAAa,GAAGC,uBAAuB,CAAC,CAAC;MAC7C,IAAID,aAAa,EAAE;QACjB,OAAOgB,uBAAuB,CAACzM,MAAM,EAAEyL,aAAa,EAAExL,OAAO,CAACQ,gBAAgB,CAAC;MACjF,CAAC,MAAM;QACL,OAAOiM,kBAAkB,CAAC1M,MAAM,EAAE2M,kBAAkB,CAAC1M,OAAO,CAAC,EAAEsL,kBAAkB,CAAC,eAAe,CAAC,CAAC;MACrG;IACF;EACF;AACF;AAEA,SAASiB,kBAAkBA,CAACxM,MAAM,EAAEQ,UAAU,EAAEoL,MAAM,EAAE;EACtD;EACA,IAAInI,MAAM,GAAG,IAAImI,MAAM,CAAC5L,MAAM,EAAEQ,UAAU,CAAC;EAE3CiD,MAAM,CAACmJ,eAAe,GAAG,IAAI;EAC7B;EACAnJ,MAAM,CAACoJ,EAAE,GAAG,UAAUC,KAAK,EAAEpF,QAAQ,EAAE;IACrC,IAAI,CAACqF,gBAAgB,CAACD,KAAK,EAAE,UAAU/E,OAAO,EAAE;MAC9CL,QAAQ,CAACK,OAAO,CAACiF,IAAI,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EACDvJ,MAAM,CAACwJ,IAAI,GAAG,UAAUlF,OAAO,EAAEmF,QAAQ,EAAE;IACzC,IAAI,CAACC,WAAW,CAACpF,OAAO,EAAEmF,QAAQ,CAAC;EACrC,CAAC;EACD,OAAOzJ,MAAM;AACf;AAEA,SAASgJ,uBAAuBA,CAACzM,MAAM,EAAEyL,aAAa,EAAE2B,mBAAmB,EAAE;EAC3E,IAAI3J,MAAM,GAAG,IAAIgI,aAAa,CAACG,MAAM,CAAC5L,MAAM,EAAAoK,aAAA;IAC1CiD,MAAM,EAAE,KAAK;IAAE;IACfC,MAAM,EAAE;EAAK,GACVF,mBAAmB,CACvB,CAAC;EACF3J,MAAM,CAAC8J,cAAc,GAAG,IAAI;EAC5B9J,MAAM,CAACwJ,IAAI,GAAG,UAASlF,OAAO,EAAEmF,QAAQ,EAAE;IACxC,IAAI,CAACC,WAAW,CAACpF,OAAO,EAAEmF,QAAQ,CAAC;EACrC,CAAC;EAEDzJ,MAAM,CAAC+J,IAAI,GAAG,YAAW;IACvB,IAAI,CAAChJ,SAAS,CAAC,CAAC;IAChB,OAAO,IAAI;EACb,CAAC;EAEDf,MAAM,CAACgK,UAAU,GAAG,YAAW;IAC7B,IAAI,CAACjJ,SAAS,CAAC,CAAC;EAClB,CAAC;EAED,OAAOf,MAAM;AACf;AAEA,SAASiJ,kBAAkBA,CAAC1M,MAAM,EAAEC,OAAO,EAAEyN,aAAa,EAAE;EAC1D;EACA,IAAIjK,MAAM,GAAGiK,aAAa,CAACC,IAAI,CAC7B3N,MAAM,EACNC,OAAO,CAACG,QAAQ,EAChBH,OAAO,CAACM,QACV,CAAC;;EAED;EACA,IAAI0M,IAAI,GAAGxJ,MAAM,CAACwJ,IAAI;EACtBxJ,MAAM,CAACwJ,IAAI,GAAG,UAAUlF,OAAO,EAAE;IAC/B,OAAOkF,IAAI,CAACjK,IAAI,CAACS,MAAM,EAAEsE,OAAO,CAAC;EACnC,CAAC;EAEDtE,MAAM,CAACmK,cAAc,GAAG,IAAI;EAC5B,OAAOnK,MAAM;AACf;;AAEA;AACA,SAASkJ,kBAAkBA,CAACkB,IAAI,EAAE;EAChCA,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;EAEjB,IAAIC,eAAe,GAAGC,OAAO,CAACC,QAAQ,CAACC,IAAI,CAAC,GAAG,CAAC;EAChD,IAAIC,eAAe,GAAGJ,eAAe,CAAC/K,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;EACjE,IAAIoL,QAAQ,GAAGL,eAAe,CAAC/K,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EAE5D,IAAIiL,QAAQ,GAAG,EAAE;EACjB,IAAIE,eAAe,EAAE;IACnBF,QAAQ,CAACrL,IAAI,CAAC,YAAY,GAAGkL,IAAI,CAACzJ,SAAS,CAAC;IAE5C,IAAI+J,QAAQ,EAAE;MACZH,QAAQ,CAACrL,IAAI,CAAC,aAAa,CAAC;IAC9B;EACF;EAEAoL,OAAO,CAACC,QAAQ,CAACzK,OAAO,CAAC,UAASuH,GAAG,EAAE;IACrC,IAAIA,GAAG,CAAC/H,OAAO,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,EAAE;MAC5CiL,QAAQ,CAACrL,IAAI,CAACmI,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EAEF,OAAOzK,MAAM,CAAC+N,MAAM,CAAC,CAAC,CAAC,EAAEP,IAAI,EAAE;IAC7BzN,QAAQ,EAAEyN,IAAI,CAACzN,QAAQ;IACvBG,QAAQ,EAAEF,MAAM,CAAC+N,MAAM,CAAC,CAAC,CAAC,EAAEP,IAAI,CAACtN,QAAQ,EAAE;MACzCyN,QAAQ,EAAE,CAACH,IAAI,CAACtN,QAAQ,IAAIsN,IAAI,CAACtN,QAAQ,CAACyN,QAAQ,IAAI,EAAE,EACvDK,MAAM,CAACL,QAAQ;IAClB,CAAC;EACH,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASM,aAAaA,CAAE7D,GAAG,EAAE;EAC3B,IAAI8D,IAAI,GAAG,IAAI/L,KAAK,CAAC,EAAE,CAAC;EACxB,IAAIgM,KAAK,GAAGnO,MAAM,CAAC0J,IAAI,CAACU,GAAG,CAAC;EAE5B,KAAK,IAAIzG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwK,KAAK,CAACjM,MAAM,EAAEyB,CAAC,EAAE,EAAE;IACrCuK,IAAI,CAACC,KAAK,CAACxK,CAAC,CAAC,CAAC,GAAGyG,GAAG,CAAC+D,KAAK,CAACxK,CAAC,CAAC,CAAC;EAChC;EAEA,OAAOuK,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS5O,aAAaA,CAACK,MAAM,EAAEyO,QAAQ,EAAE;EACvC,IAAI9K,EAAE,GAAG,IAAI;EACb,IAAI1D,OAAO,GAAGwO,QAAQ,IAAI,CAAC,CAAC;EAE5B,IAAI,CAACzO,MAAM,GAAGA,MAAM,IAAI8L,gBAAgB,CAAC,CAAC;EAC1C,IAAI,CAACrI,MAAM,GAAG8I,WAAW,CAAC,IAAI,CAACvM,MAAM,EAAEC,OAAO,CAAC;EAC/C,IAAI,CAACmE,SAAS,GAAGnE,OAAO,CAACmE,SAAS;EAClC,IAAI,CAAC7D,QAAQ,GAAGN,OAAO,CAACM,QAAQ;EAChC,IAAI,CAACH,QAAQ,GAAGH,OAAO,CAACG,QAAQ;EAChC,IAAI,CAACI,UAAU,GAAGP,OAAO,CAACO,UAAU;EACpC,IAAI,CAACC,gBAAgB,GAAGR,OAAO,CAACQ,gBAAgB;EAChD,IAAI,CAACM,sBAAsB,GAAGd,OAAO,CAACc,sBAAsB;;EAE5D;EACA,IAAI,CAACf,MAAM,EAAE;IACX,IAAI,CAACyD,MAAM,CAACiL,KAAK,GAAG,IAAI;EAC1B;;EAEA;EACA,IAAI,CAACC,YAAY,GAAG,EAAE;EACtB,IAAI,CAAClL,MAAM,CAACoJ,EAAE,CAAC,SAAS,EAAE,UAAU+B,QAAQ,EAAE;IAC5C,IAAIjL,EAAE,CAACG,UAAU,EAAE;MACjB;IACF;IACA,IAAI,OAAO8K,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,OAAO,EAAE;MACxDjL,EAAE,CAACF,MAAM,CAACiL,KAAK,GAAG,IAAI;MACtBG,sBAAsB,CAAC,CAAC;IAC1B,CAAC,MAAM;MACL;MACA,IAAIC,EAAE,GAAGF,QAAQ,CAACE,EAAE;MACpB,IAAIrM,IAAI,GAAGkB,EAAE,CAACoL,UAAU,CAACD,EAAE,CAAC;MAC5B,IAAIrM,IAAI,KAAK4I,SAAS,EAAE;QACtB,IAAIuD,QAAQ,CAACI,OAAO,EAAE;UACpB,IAAIvM,IAAI,CAACxC,OAAO,IAAI,OAAOwC,IAAI,CAACxC,OAAO,CAAC4M,EAAE,KAAK,UAAU,EAAE;YACzDpK,IAAI,CAACxC,OAAO,CAAC4M,EAAE,CAAC+B,QAAQ,CAACK,OAAO,CAAC;UACnC;QACF,CAAC,MAAM;UACL;UACA,OAAOtL,EAAE,CAACoL,UAAU,CAACD,EAAE,CAAC;;UAExB;UACA,IAAInL,EAAE,CAACuL,WAAW,KAAK,IAAI,EAAE;YAC3B;YACAvL,EAAE,CAACa,SAAS,CAAC,CAAC;UAChB;;UAEA;UACA,IAAIoK,QAAQ,CAACzH,KAAK,EAAE;YAClB1E,IAAI,CAACJ,QAAQ,CAACkC,MAAM,CAAC+J,aAAa,CAACM,QAAQ,CAACzH,KAAK,CAAC,CAAC;UACrD,CAAC,MACI;YACH1E,IAAI,CAACJ,QAAQ,CAACiC,OAAO,CAACsK,QAAQ,CAAC5H,MAAM,CAAC;UACxC;QACF;MACF;IACF;EACF,CAAC,CAAC;;EAEF;EACA,SAASmI,OAAOA,CAAChI,KAAK,EAAE;IACtBxD,EAAE,CAACG,UAAU,GAAG,IAAI;IAEpB,KAAK,IAAIgL,EAAE,IAAInL,EAAE,CAACoL,UAAU,EAAE;MAC5B,IAAIpL,EAAE,CAACoL,UAAU,CAACD,EAAE,CAAC,KAAKzD,SAAS,EAAE;QACnC1H,EAAE,CAACoL,UAAU,CAACD,EAAE,CAAC,CAACzM,QAAQ,CAACkC,MAAM,CAAC4C,KAAK,CAAC;MAC1C;IACF;IACAxD,EAAE,CAACoL,UAAU,GAAG1O,MAAM,CAAC+O,MAAM,CAAC,IAAI,CAAC;EACrC;;EAEA;EACA,SAASP,sBAAsBA,CAAA,EAC/B;IAAA,IAAAQ,SAAA,GAAAlH,0BAAA,CACuBxE,EAAE,CAACgL,YAAY,CAAChK,MAAM,CAAC,CAAC,CAAC;MAAA2K,KAAA;IAAA;MAA9C,KAAAD,SAAA,CAAAxI,CAAA,MAAAyI,KAAA,GAAAD,SAAA,CAAA1G,CAAA,IAAAC,IAAA,GAAgD;QAAA,IAAtC2G,OAAO,GAAAD,KAAA,CAAAvJ,KAAA;QACfpC,EAAE,CAACF,MAAM,CAACwJ,IAAI,CAACsC,OAAO,CAACxH,OAAO,EAAEwH,OAAO,CAACrC,QAAQ,CAAC;MACnD;IAAC,SAAAzI,GAAA;MAAA4K,SAAA,CAAAxG,CAAA,CAAApE,GAAA;IAAA;MAAA4K,SAAA,CAAAxK,CAAA;IAAA;EACH;EAEA,IAAIpB,MAAM,GAAG,IAAI,CAACA,MAAM;EACxB;EACA,IAAI,CAACA,MAAM,CAACoJ,EAAE,CAAC,OAAO,EAAEsC,OAAO,CAAC;EAChC,IAAI,CAAC1L,MAAM,CAACoJ,EAAE,CAAC,MAAM,EAAE,UAAU2C,QAAQ,EAAEC,UAAU,EAAE;IACrD,IAAI1H,OAAO,GAAG,6CAA6C;IAE3DA,OAAO,IAAI,iBAAiB,GAAGyH,QAAQ,GAAG,KAAK;IAC/CzH,OAAO,IAAI,mBAAmB,GAAG0H,UAAU,GAAG,KAAK;IAEnD1H,OAAO,IAAI,0BAA0B,GAAIpE,EAAE,CAAC3D,MAAM,GAAG,KAAK;IAC1D+H,OAAO,IAAI,kBAAkB,GAAItE,MAAM,CAACiM,SAAS,GAAG,KAAK;IACzD3H,OAAO,IAAI,kBAAkB,GAAGtE,MAAM,CAACkM,SAAS,GAAG,KAAK;IAExD5H,OAAO,IAAI,eAAe,GAAGtE,MAAM,CAAC4J,MAAM,GAAG,KAAK;IAClDtF,OAAO,IAAI,eAAe,GAAGtE,MAAM,CAAC6J,MAAM,GAAG,KAAK;IAElD6B,OAAO,CAAC,IAAI3M,KAAK,CAACuF,OAAO,CAAC,CAAC;EAC7B,CAAC,CAAC;EAEF,IAAI,CAACgH,UAAU,GAAG1O,MAAM,CAAC+O,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEvC,IAAI,CAACF,WAAW,GAAG,KAAK;EACxB,IAAI,CAACpL,UAAU,GAAG,KAAK;EACvB,IAAI,CAAC8L,QAAQ,GAAG,KAAK;EACrB,IAAI,CAACC,kBAAkB,GAAG,IAAI;EAC9B,IAAI,CAACC,MAAM,GAAG,CAAC;AACjB;;AAEA;AACA;AACA;AACA;AACAnQ,aAAa,CAACmC,SAAS,CAACwB,OAAO,GAAG,YAAY;EAC5C,OAAO,IAAI,CAACvB,IAAI,CAAC,SAAS,CAAC;AAC7B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACApC,aAAa,CAACmC,SAAS,CAACC,IAAI,GAAG,UAASC,MAAM,EAAEC,MAAM,EAAEI,QAAQ,EAAEpC,OAAO,EAAE;EACzE,IAAI,CAACoC,QAAQ,EAAE;IACbA,QAAQ,GAAG5C,OAAO,CAAC6C,KAAK,CAAC,CAAC;EAC5B;;EAEA;EACA,IAAIwM,EAAE,GAAG,EAAE,IAAI,CAACgB,MAAM;;EAEtB;EACA,IAAI,CAACf,UAAU,CAACD,EAAE,CAAC,GAAG;IACpBA,EAAE,EAAEA,EAAE;IACNzM,QAAQ,EAAEA,QAAQ;IAClBpC,OAAO,EAAEA;EACX,CAAC;;EAED;EACA,IAAIsP,OAAO,GAAG;IACZxH,OAAO,EAAE;MACP+G,EAAE,EAAEA,EAAE;MACN9M,MAAM,EAAEA,MAAM;MACdC,MAAM,EAAEA;IACV,CAAC;IACDiL,QAAQ,EAAEjN,OAAO,IAAIA,OAAO,CAACiN;EAC/B,CAAC;EAED,IAAI,IAAI,CAACpJ,UAAU,EAAE;IACnBzB,QAAQ,CAACkC,MAAM,CAAC,IAAI/B,KAAK,CAAC,sBAAsB,CAAC,CAAC;EACpD,CAAC,MAAM,IAAI,IAAI,CAACiB,MAAM,CAACiL,KAAK,EAAE;IAC5B;IACA,IAAI,CAACjL,MAAM,CAACwJ,IAAI,CAACsC,OAAO,CAACxH,OAAO,EAAEwH,OAAO,CAACrC,QAAQ,CAAC;EACrD,CAAC,MAAM;IACL,IAAI,CAACyB,YAAY,CAAChM,IAAI,CAAC4M,OAAO,CAAC;EACjC;;EAEA;EACA,IAAI5L,EAAE,GAAG,IAAI;EACb,OAAOtB,QAAQ,CAACQ,OAAO,SAAM,CAAC,UAAUsE,KAAK,EAAE;IAC7C,IAAIA,KAAK,YAAY1H,OAAO,CAAC4H,iBAAiB,IAAIF,KAAK,YAAY1H,OAAO,CAAC+H,YAAY,EAAE;MACvF;MACA;MACA,OAAO7D,EAAE,CAACoL,UAAU,CAACD,EAAE,CAAC;;MAExB;MACA,OAAOnL,EAAE,CAACsB,kBAAkB,CAAC,IAAI,CAAC,CAC/B5B,IAAI,CAAC,YAAW;QACf,MAAM8D,KAAK;MACb,CAAC,EAAE,UAAS1C,GAAG,EAAE;QACf,MAAMA,GAAG;MACX,CAAC,CAAC;IACN,CAAC,MAAM;MACL,MAAM0C,KAAK;IACb;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACAxH,aAAa,CAACmC,SAAS,CAACmC,IAAI,GAAG,YAAY;EACzC,OAAO,IAAI,CAAC2L,QAAQ,IAAIvP,MAAM,CAAC0J,IAAI,CAAC,IAAI,CAACgF,UAAU,CAAC,CAACxM,MAAM,GAAG,CAAC;AACjE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA5C,aAAa,CAACmC,SAAS,CAAC0C,SAAS,GAAG,UAAUI,KAAK,EAAE8C,QAAQ,EAAE;EAC7D,IAAI/D,EAAE,GAAG,IAAI;EACb,IAAIiB,KAAK,EAAE;IACT;IACA,KAAK,IAAIkK,EAAE,IAAI,IAAI,CAACC,UAAU,EAAE;MAC9B,IAAI,IAAI,CAACA,UAAU,CAACD,EAAE,CAAC,KAAKzD,SAAS,EAAE;QACrC,IAAI,CAAC0D,UAAU,CAACD,EAAE,CAAC,CAACzM,QAAQ,CAACkC,MAAM,CAAC,IAAI/B,KAAK,CAAC,mBAAmB,CAAC,CAAC;MACrE;IACF;IACA,IAAI,CAACuM,UAAU,GAAG1O,MAAM,CAAC+O,MAAM,CAAC,IAAI,CAAC;EACvC;EAEA,IAAI,OAAO1H,QAAQ,KAAK,UAAU,EAAE;IAClC,IAAI,CAACmI,kBAAkB,GAAGnI,QAAQ;EACpC;EACA,IAAI,CAAC,IAAI,CAACzD,IAAI,CAAC,CAAC,EAAE;IAChB;IACA,IAAI8L,OAAO,GAAG,SAAVA,OAAOA,CAAYtL,GAAG,EAAE;MAC1Bd,EAAE,CAACG,UAAU,GAAG,IAAI;MACpBH,EAAE,CAACiM,QAAQ,GAAG,KAAK;MACnB,IAAIjM,EAAE,CAACF,MAAM,IAAI,IAAI,IAAIE,EAAE,CAACF,MAAM,CAACuM,kBAAkB,EAAE;QACrD;QACArM,EAAE,CAACF,MAAM,CAACuM,kBAAkB,CAAC,SAAS,CAAC;MACzC;MACArM,EAAE,CAACF,MAAM,GAAG,IAAI;MAChBE,EAAE,CAACuL,WAAW,GAAG,KAAK;MACtB,IAAIvL,EAAE,CAACkM,kBAAkB,EAAE;QACzBlM,EAAE,CAACkM,kBAAkB,CAACpL,GAAG,EAAEd,EAAE,CAAC;MAChC,CAAC,MAAM,IAAIc,GAAG,EAAE;QACd,MAAMA,GAAG;MACX;IACF,CAAC;IAED,IAAI,IAAI,CAAChB,MAAM,EAAE;MACf,IAAI,OAAO,IAAI,CAACA,MAAM,CAAC+J,IAAI,KAAK,UAAU,EAAE;QAC1C,IAAI,IAAI,CAAC/J,MAAM,CAACwM,MAAM,EAAE;UACtBF,OAAO,CAAC,IAAIvN,KAAK,CAAC,wBAAwB,CAAC,CAAC;UAC5C;QACF;;QAEA;QACA,IAAI0N,gBAAgB,GAAG3I,UAAU,CAAC,YAAW;UAC3C,IAAI5D,EAAE,CAACF,MAAM,EAAE;YACbE,EAAE,CAACF,MAAM,CAAC+J,IAAI,CAAC,CAAC;UAClB;QACF,CAAC,EAAE,IAAI,CAACzM,sBAAsB,CAAC;QAE/B,IAAI,CAAC0C,MAAM,CAAC0M,IAAI,CAAC,MAAM,EAAE,YAAW;UAClC1I,YAAY,CAACyI,gBAAgB,CAAC;UAC9B,IAAIvM,EAAE,CAACF,MAAM,EAAE;YACbE,EAAE,CAACF,MAAM,CAACwM,MAAM,GAAG,IAAI;UACzB;UACAF,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;QAEF,IAAI,IAAI,CAACtM,MAAM,CAACiL,KAAK,EAAE;UACrB,IAAI,CAACjL,MAAM,CAACwJ,IAAI,CAACzB,mBAAmB,CAAC;QACvC,CAAC,MAAM;UACL,IAAI,CAACmD,YAAY,CAAChM,IAAI,CAAC;YAAEoF,OAAO,EAAEyD;UAAoB,CAAC,CAAC;QAC1D;;QAEA;QACA;QACA,IAAI,CAACoE,QAAQ,GAAG,IAAI;QACpB;MACF,CAAC,MACI,IAAI,OAAO,IAAI,CAACnM,MAAM,CAACe,SAAS,KAAK,UAAU,EAAE;QACpD,IAAI,CAACf,MAAM,CAACe,SAAS,CAAC,CAAC,CAAC,CAAC;QACzB,IAAI,CAACf,MAAM,CAACwM,MAAM,GAAG,IAAI;MAC3B,CAAC,MACI;QACH,MAAM,IAAIzN,KAAK,CAAC,4BAA4B,CAAC;MAC/C;IACF;IACAuN,OAAO,CAAC,CAAC;EACX,CAAC,MACI;IACH;IACA,IAAI,CAACb,WAAW,GAAG,IAAI;EACzB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAvP,aAAa,CAACmC,SAAS,CAACmD,kBAAkB,GAAG,UAAUL,KAAK,EAAElC,OAAO,EAAE;EACrE,IAAIL,QAAQ,GAAG5C,OAAO,CAAC6C,KAAK,CAAC,CAAC;EAC9B,IAAII,OAAO,EAAE;IACXL,QAAQ,CAACQ,OAAO,CAACH,OAAO,CAACA,OAAO,CAAC;EACnC;EACA,IAAI,CAAC8B,SAAS,CAACI,KAAK,EAAE,UAASH,GAAG,EAAEhB,MAAM,EAAE;IAC1C,IAAIgB,GAAG,EAAE;MACPpC,QAAQ,CAACkC,MAAM,CAACE,GAAG,CAAC;IACtB,CAAC,MAAM;MACLpC,QAAQ,CAACiC,OAAO,CAACb,MAAM,CAAC;IAC1B;EACF,CAAC,CAAC;EACF,OAAOpB,QAAQ,CAACQ,OAAO;AACzB,CAAC;AAEDoD,MAAM,CAACC,OAAO,GAAGvG,aAAa;AAC9BsG,uCAAuC,GAAGyF,uBAAuB;AACjEzF,kCAAkC,GAAGyG,kBAAkB;AACvDzG,kCAAkC,GAAGuG,kBAAkB;AACvDvG,uCAAuC,GAAGwG,uBAAuB;AACjExG,kCAAkC,GAAGpE,mBAAmB;;;;;;;;AC3f3C;;AAEb,IAAI2O,SAAS,GAAG,KAAK;AACrBvK,MAAM,CAACC,OAAO,GAAGrG,kBAAkB;AACnC,SAASA,kBAAkBA,CAAA,EAAG;EAC5B,IAAI,CAAC4Q,KAAK,GAAGpQ,MAAM,CAAC+O,MAAM,CAAC,IAAI,CAAC;EAChC,IAAI,CAAC7M,MAAM,GAAG,CAAC;AACjB;AAEA1C,kBAAkB,CAACiC,SAAS,CAAC8D,uBAAuB,GAAG,UAAS8K,QAAQ,EAAE;EACxE,OAAO,IAAI,CAACD,KAAK,CAACC,QAAQ,CAAC,KAAK,IAAI,EAAE;IACpCA,QAAQ,EAAE;EACZ;EAEA,IAAIA,QAAQ,IAAIF,SAAS,EAAE;IACzB,MAAM,IAAIhO,KAAK,CAAC,uCAAuC,GAAGkO,QAAQ,GAAG,KAAK,GAAGF,SAAU,CAAC;EAC1F;EAEA,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAC,GAAG,IAAI;EAC3B,IAAI,CAACnO,MAAM,EAAE;EACb,OAAOmO,QAAQ;AACjB,CAAC;AAED7Q,kBAAkB,CAACiC,SAAS,CAACqC,WAAW,GAAG,UAASwM,IAAI,EAAE;EACxD,OAAO,IAAI,CAACF,KAAK,CAACE,IAAI,CAAC;EACvB,IAAI,CAACpO,MAAM,EAAE;AACf,CAAC;;;;;;;AC1BD,IAAIgJ,kBAAkB,GAAG7L,mBAAO,CAAC,GAAsB,CAAC;;AAExD;AACA,IAAIkR,MAAM,GAAG,SAATA,MAAMA,CAAaC,WAAW,EAAE;EAClC,OACE,OAAOA,WAAW,KAAK,WAAW,IAClCA,WAAW,CAACC,QAAQ,IAAI,IAAI,IAC5BD,WAAW,CAACC,QAAQ,CAACC,IAAI,IAAI,IAAI;AAErC,CAAC;AACD9K,qBAAqB,GAAG2K,MAAM;;AAE9B;AACA3K,uBAAuB,GAAG,OAAO8H,OAAO,KAAK,WAAW,IAAI6C,MAAM,CAAC7C,OAAO,CAAC,GACvE,MAAM,GACN,SAAS;;AAEb;AACA;AACA,IAAIiD,cAAc,GAAGC,qBAAqB,CAAC,gBAAgB,CAAC;AAC5DhL,2BAA2B,GAAGA,MAAM,CAACC,OAAO,CAAC6F,QAAQ,KAAK,MAAM,GAC3D,CAAC,CAACiF,cAAc,IAAIA,cAAc,CAACE,YAAY,KAAK,CAACnD,OAAO,CAACoD,SAAS,GACvE,OAAOC,MAAM,KAAK,WAAW;;AAEjC;AACAnL,mBAAmB,GAAGA,MAAM,CAACC,OAAO,CAAC6F,QAAQ,KAAK,SAAS,GACvDsF,IAAI,CAACC,SAAS,CAACC,mBAAmB,GAClChG,kBAAkB,CAAC,IAAI,CAAC,CAACjK,IAAI,CAAC,CAAC,CAACiB,MAAM;AAE1C,SAAS0O,qBAAqBA,CAAEhL,MAAM,EAAE;EACtC,IAAI;IACF,OAAOsF,kBAAkB,CAACtF,MAAM,CAAC;EACnC,CAAC,CAAC,OAAMxB,GAAG,EAAE;IACX,OAAO,IAAI;EACb;AACF;;;;;;;ACnCA;AACA;AACA;AACA;AACA;AACAwB,MAAM,CAACC,OAAO,GAAG,kiHAAkiH;;;;;;;ACLnjH;AACA,IAAIqF,kBAAkB,GAAGiG,IAAI,CACzB,mCAAmC,GACnC,YAAY,GACZ,+EACJ,CAAC;AAEDvL,MAAM,CAACC,OAAO,GAAGqF,kBAAkB;;;;;;;ACPnC;AACA;AACA;AACA;AACA;AACA;AACA,SAASkG,QAAQA,CAAC1J,OAAO,EAAEmF,QAAQ,EAAE;EACnC,IAAI,CAACnF,OAAO,GAAGA,OAAO;EACtB,IAAI,CAACmF,QAAQ,GAAGA,QAAQ;AAC1B;AAEAjH,MAAM,CAACC,OAAO,GAAGuL,QAAQ;;;;;;;;ACXzB;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAG/R,mBAAO,CAAC,GAAY,CAAC;;AAEpC;AACA,IAAI6L,kBAAkB,GAAGiG,IAAI,CACzB,kCAAkC,GAClC,YAAY,GACZ,gFACJ,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAIhG,mBAAmB,GAAG,0BAA0B;;AAEpD;;AAEA;AACA;AACA,IAAI/H,MAAM,GAAG;EACXiO,IAAI,EAAE,SAAAA,KAAA,EAAW,CAAC;AACpB,CAAC;AACD,IAAI,OAAOL,IAAI,KAAK,WAAW,IAAI,OAAOlE,WAAW,KAAK,UAAU,IAAI,OAAOJ,gBAAgB,KAAK,UAAU,EAAE;EAC9G;EACAtJ,MAAM,CAACoJ,EAAE,GAAG,UAAUC,KAAK,EAAEpF,QAAQ,EAAE;IACrCqF,gBAAgB,CAACD,KAAK,EAAE,UAAU/E,OAAO,EAAE;MACzCL,QAAQ,CAACK,OAAO,CAACiF,IAAI,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EACDvJ,MAAM,CAACwJ,IAAI,GAAG,UAAUlF,OAAO,EAAE;IAC/BoF,WAAW,CAACpF,OAAO,CAAC;EACtB,CAAC;AACH,CAAC,MACI,IAAI,OAAOgG,OAAO,KAAK,WAAW,EAAE;EACvC;;EAEA,IAAItC,aAAa;EACjB,IAAI;IACFA,aAAa,GAAGF,kBAAkB,CAAC,gBAAgB,CAAC;EACtD,CAAC,CAAC,OAAMpE,KAAK,EAAE;IACb,IAAI6D,OAAA,CAAO7D,KAAK,MAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,CAAC0E,IAAI,KAAK,kBAAkB,EAAE;MACpF;IAAA,CACD,MAAM;MACL,MAAM1E,KAAK;IACb;EACF;EAEA,IAAIsE,aAAa,IACf;EACAA,aAAa,CAACkG,UAAU,KAAK,IAAI,EAAE;IACnC,IAAIA,UAAU,GAAIlG,aAAa,CAACkG,UAAU;IAC1ClO,MAAM,CAACwJ,IAAI,GAAG0E,UAAU,CAACxE,WAAW,CAACvL,IAAI,CAAC+P,UAAU,CAAC;IACrDlO,MAAM,CAACoJ,EAAE,GAAG8E,UAAU,CAAC9E,EAAE,CAACjL,IAAI,CAAC+P,UAAU,CAAC;IAC1ClO,MAAM,CAACiO,IAAI,GAAG3D,OAAO,CAAC2D,IAAI,CAAC9P,IAAI,CAACmM,OAAO,CAAC;EAC1C,CAAC,MAAM;IACLtK,MAAM,CAACoJ,EAAE,GAAGkB,OAAO,CAAClB,EAAE,CAACjL,IAAI,CAACmM,OAAO,CAAC;IACpC;IACAtK,MAAM,CAACwJ,IAAI,GAAG,UAAUlF,OAAO,EAAE;MAC/BgG,OAAO,CAACd,IAAI,CAAClF,OAAO,CAAC;IACvB,CAAC;IACD;IACAtE,MAAM,CAACoJ,EAAE,CAAC,YAAY,EAAE,YAAY;MAClCkB,OAAO,CAAC2D,IAAI,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC;IACFjO,MAAM,CAACiO,IAAI,GAAG3D,OAAO,CAAC2D,IAAI,CAAC9P,IAAI,CAACmM,OAAO,CAAC;EAC1C;AACF,CAAC,MACI;EACH,MAAM,IAAIvL,KAAK,CAAC,qCAAqC,CAAC;AACxD;AAEA,SAASoP,YAAYA,CAACzK,KAAK,EAAE;EAC3B,OAAO9G,MAAM,CAACwR,mBAAmB,CAAC1K,KAAK,CAAC,CAAC2K,MAAM,CAAC,UAASC,OAAO,EAAE7J,IAAI,EAAE;IACtE,OAAO7H,MAAM,CAACmK,cAAc,CAACuH,OAAO,EAAE7J,IAAI,EAAE;MAC/CnC,KAAK,EAAEoB,KAAK,CAACe,IAAI,CAAC;MAClBgC,UAAU,EAAE;IACT,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8H,SAASA,CAACjM,KAAK,EAAE;EACxB,OAAOA,KAAK,IAAK,OAAOA,KAAK,CAAC1C,IAAI,KAAK,UAAW,IAAK,OAAO0C,KAAK,SAAM,KAAK,UAAW;AAC3F;;AAEA;AACAtC,MAAM,CAACH,OAAO,GAAG,CAAC,CAAC;;AAEnB;AACA;AACA;AACA;AACA;AACA;AACAG,MAAM,CAACH,OAAO,CAAC2O,GAAG,GAAG,SAASA,GAAGA,CAAChL,EAAE,EAAEiL,IAAI,EAAE;EAC1C,IAAIrN,CAAC,GAAG,IAAIsN,QAAQ,CAAC,UAAU,GAAGlL,EAAE,GAAG,2BAA2B,CAAC;EACnE,OAAOpC,CAAC,CAACsF,KAAK,CAACtF,CAAC,EAAEqN,IAAI,CAAC;AACzB,CAAC;;AAED;AACA;AACA;AACA;AACAzO,MAAM,CAACH,OAAO,CAACA,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;EAC1C,OAAOjD,MAAM,CAAC0J,IAAI,CAACtG,MAAM,CAACH,OAAO,CAAC;AACpC,CAAC;;AAED;AACA;AACA;AACAG,MAAM,CAACoM,kBAAkB,GAAGxE,SAAS;;AAErC;AACA;AACA;AACA;AACA;AACA5H,MAAM,CAAC2O,cAAc,GAAG,UAASvG,IAAI,EAAE;EACrC,IAAIwG,KAAK,GAAG,SAARA,KAAKA,CAAA,EAAc;IACrB5O,MAAM,CAACiO,IAAI,CAAC7F,IAAI,CAAC;EACnB,CAAC;EAED,IAAG,CAACpI,MAAM,CAACoM,kBAAkB,EAAE;IAC7B,OAAOwC,KAAK,CAAC,CAAC;EAChB;EAEA,IAAIrL,MAAM,GAAGvD,MAAM,CAACoM,kBAAkB,CAAChE,IAAI,CAAC;EAC5C,IAAImG,SAAS,CAAChL,MAAM,CAAC,EAAE;IACrBA,MAAM,CAAC3D,IAAI,CAACgP,KAAK,EAAEA,KAAK,CAAC;EAC3B,CAAC,MAAM;IACLA,KAAK,CAAC,CAAC;EACT;AACF,CAAC;AAED,IAAIC,gBAAgB,GAAG,IAAI;AAE3B7O,MAAM,CAACoJ,EAAE,CAAC,SAAS,EAAE,UAAU0C,OAAO,EAAE;EACtC,IAAIA,OAAO,KAAK/D,mBAAmB,EAAE;IACnC,OAAO/H,MAAM,CAAC2O,cAAc,CAAC,CAAC,CAAC;EACjC;EACA,IAAI;IACF,IAAIpQ,MAAM,GAAGyB,MAAM,CAACH,OAAO,CAACiM,OAAO,CAACvN,MAAM,CAAC;IAE3C,IAAIA,MAAM,EAAE;MACVsQ,gBAAgB,GAAG/C,OAAO,CAACT,EAAE;;MAE7B;MACA,IAAI9H,MAAM,GAAGhF,MAAM,CAACmI,KAAK,CAACnI,MAAM,EAAEuN,OAAO,CAACtN,MAAM,CAAC;MAEjD,IAAI+P,SAAS,CAAChL,MAAM,CAAC,EAAE;QACrB;QACAA,MAAM,CACD3D,IAAI,CAAC,UAAU2D,MAAM,EAAE;UACtB,IAAIA,MAAM,YAAYyK,QAAQ,EAAE;YAC9BhO,MAAM,CAACwJ,IAAI,CAAC;cACV6B,EAAE,EAAES,OAAO,CAACT,EAAE;cACd9H,MAAM,EAAEA,MAAM,CAACe,OAAO;cACtBZ,KAAK,EAAE;YACT,CAAC,EAAEH,MAAM,CAACkG,QAAQ,CAAC;UACrB,CAAC,MAAM;YACLzJ,MAAM,CAACwJ,IAAI,CAAC;cACV6B,EAAE,EAAES,OAAO,CAACT,EAAE;cACd9H,MAAM,EAAEA,MAAM;cACdG,KAAK,EAAE;YACT,CAAC,CAAC;UACJ;UACAmL,gBAAgB,GAAG,IAAI;QACzB,CAAC,CAAC,SACI,CAAC,UAAU7N,GAAG,EAAE;UACpBhB,MAAM,CAACwJ,IAAI,CAAC;YACV6B,EAAE,EAAES,OAAO,CAACT,EAAE;YACd9H,MAAM,EAAE,IAAI;YACZG,KAAK,EAAEyK,YAAY,CAACnN,GAAG;UACzB,CAAC,CAAC;UACF6N,gBAAgB,GAAG,IAAI;QACzB,CAAC,CAAC;MACR,CAAC,MACI;QACH;QACA,IAAItL,MAAM,YAAYyK,QAAQ,EAAE;UAC9BhO,MAAM,CAACwJ,IAAI,CAAC;YACV6B,EAAE,EAAES,OAAO,CAACT,EAAE;YACd9H,MAAM,EAAEA,MAAM,CAACe,OAAO;YACtBZ,KAAK,EAAE;UACT,CAAC,EAAEH,MAAM,CAACkG,QAAQ,CAAC;QACrB,CAAC,MAAM;UACLzJ,MAAM,CAACwJ,IAAI,CAAC;YACV6B,EAAE,EAAES,OAAO,CAACT,EAAE;YACd9H,MAAM,EAAEA,MAAM;YACdG,KAAK,EAAE;UACT,CAAC,CAAC;QACJ;QAEAmL,gBAAgB,GAAG,IAAI;MACzB;IACF,CAAC,MACI;MACH,MAAM,IAAI9P,KAAK,CAAC,kBAAkB,GAAG+M,OAAO,CAACvN,MAAM,GAAG,GAAG,CAAC;IAC5D;EACF,CAAC,CACD,OAAOyC,GAAG,EAAE;IACVhB,MAAM,CAACwJ,IAAI,CAAC;MACV6B,EAAE,EAAES,OAAO,CAACT,EAAE;MACd9H,MAAM,EAAE,IAAI;MACZG,KAAK,EAAEyK,YAAY,CAACnN,GAAG;IACzB,CAAC,CAAC;EACJ;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACAhB,MAAM,CAAC8O,QAAQ,GAAG,UAAUjP,OAAO,EAAErD,OAAO,EAAE;EAE5C,IAAIqD,OAAO,EAAE;IACX,KAAK,IAAI4E,IAAI,IAAI5E,OAAO,EAAE;MACxB,IAAIA,OAAO,CAACkP,cAAc,CAACtK,IAAI,CAAC,EAAE;QAChCzE,MAAM,CAACH,OAAO,CAAC4E,IAAI,CAAC,GAAG5E,OAAO,CAAC4E,IAAI,CAAC;MACtC;IACF;EACF;EAEA,IAAIjI,OAAO,EAAE;IACXwD,MAAM,CAACoM,kBAAkB,GAAG5P,OAAO,CAACwS,WAAW;EACjD;EAEAhP,MAAM,CAACwJ,IAAI,CAAC,OAAO,CAAC;AACtB,CAAC;AAEDxJ,MAAM,CAACiP,IAAI,GAAG,UAAUzD,OAAO,EAAE;EAC/B,IAAIqD,gBAAgB,EAAE;IACpB,IAAIrD,OAAO,YAAYwC,QAAQ,EAAE;MAC/BhO,MAAM,CAACwJ,IAAI,CAAC;QACV6B,EAAE,EAAEwD,gBAAgB;QACpBtD,OAAO,EAAE,IAAI;QACbC,OAAO,EAAEA,OAAO,CAAClH;MACnB,CAAC,EAAEkH,OAAO,CAAC/B,QAAQ,CAAC;MACpB;IACF;IAEAzJ,MAAM,CAACwJ,IAAI,CAAC;MACV6B,EAAE,EAAEwD,gBAAgB;MACpBtD,OAAO,EAAE,IAAI;MACbC,OAAO,EAAPA;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AAED,IAAI,IAA8B,EAAE;EAClC/I,WAAW,GAAGzC,MAAM,CAAC8O,QAAQ;EAC7BrM,YAAY,GAAGzC,MAAM,CAACiP,IAAI;AAC5B;;;;;;UCtQA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;;;ACtBA,IAAI9S,WAAW,GAAGF,mBAAO,CAAC,GAAe,CAAC;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACAwG,YAAY,GAAG,SAAS9C,IAAIA,CAACpD,MAAM,EAAEC,OAAO,EAAE;EAC5C,IAAIF,IAAI,GAAGL,mBAAO,CAAC,GAAQ,CAAC;EAE5B,OAAO,IAAIK,IAAI,CAACC,MAAM,EAAEC,OAAO,CAAC;AAClC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACAiG,cAAc,GAAG,SAASzC,MAAMA,CAACH,OAAO,EAAErD,OAAO,EAAE;EACjD,IAAIwD,MAAM,GAAG/D,mBAAO,CAAC,GAAU,CAAC;EAChC+D,MAAM,CAACkP,GAAG,CAACrP,OAAO,EAAErD,OAAO,CAAC;AAC9B,CAAC;;AAED;AACA;AACA;AACA;AACAiG,kBAAkB,GAAG,SAAS0M,UAAUA,CAAC3D,OAAO,EAAE;EAChD,IAAIxL,MAAM,GAAG/D,mBAAO,CAAC,GAAU,CAAC;EAChC+D,MAAM,CAACiP,IAAI,CAACzD,OAAO,CAAC;AACtB,CAAC;;AAED;AACA;AACA;AACA;AACA/I,0CAAsC;;AAEtC;AACA;AACA;AACA;AACAA,2CAAwC;AAExCA,gBAAgB,GAAGtG,WAAW,CAACmM,QAAQ;AACvC7F,oBAAoB,GAAGtG,WAAW,CAACsR,YAAY;AAC/ChL,YAAY,GAAGtG,WAAW,CAAC0B,IAAI", "sources": ["webpack://workerpool/webpack/universalModuleDefinition", "webpack://workerpool/./src/Pool.js", "webpack://workerpool/./src/Promise.js", "webpack://workerpool/./src/WorkerHandler.js", "webpack://workerpool/./src/debug-port-allocator.js", "webpack://workerpool/./src/environment.js", "webpack://workerpool/./src/generated/embeddedWorker.js", "webpack://workerpool/./src/requireFoolWebpack.js", "webpack://workerpool/./src/transfer.js", "webpack://workerpool/./src/worker.js", "webpack://workerpool/webpack/bootstrap", "webpack://workerpool/./src/index.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"workerpool\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"workerpool\"] = factory();\n\telse\n\t\troot[\"workerpool\"] = factory();\n})((typeof self !== 'undefined' ? self : this), function() {\nreturn ", "var Promise = require('./Promise');\nvar WorkerHandler = require('./WorkerHandler');\nvar environment = require('./environment');\nvar DebugPortAllocator = require('./debug-port-allocator');\nvar DEBUG_PORT_ALLOCATOR = new DebugPortAllocator();\n/**\n * A pool to manage workers\n * @param {String} [script]   Optional worker script\n * @param {WorkerPoolOptions} [options]  See docs\n * @constructor\n */\nfunction Pool(script, options) {\n  if (typeof script === 'string') {\n    this.script = script || null;\n  }\n  else {\n    this.script = null;\n    options = script;\n  }\n\n  this.workers = [];  // queue with all workers\n  this.tasks = [];    // queue with tasks awaiting execution\n\n  options = options || {};\n\n  this.forkArgs = Object.freeze(options.forkArgs || []);\n  this.forkOpts = Object.freeze(options.forkOpts || {});\n  this.workerOpts = Object.freeze(options.workerOpts || {});\n  this.workerThreadOpts = Object.freeze(options.workerThreadOpts || {})\n  this.debugPortStart = (options.debugPortStart || 43210);\n  this.nodeWorker = options.nodeWorker;\n  this.workerType = options.workerType || options.nodeWorker || 'auto'\n  this.maxQueueSize = options.maxQueueSize || Infinity;\n  this.workerTerminateTimeout = options.workerTerminateTimeout || 1000;\n\n  this.onCreateWorker = options.onCreateWorker || (() => null);\n  this.onTerminateWorker = options.onTerminateWorker || (() => null);\n\n  // configuration\n  if (options && 'maxWorkers' in options) {\n    validateMaxWorkers(options.maxWorkers);\n    this.maxWorkers = options.maxWorkers;\n  }\n  else {\n    this.maxWorkers = Math.max((environment.cpus || 4) - 1, 1);\n  }\n\n  if (options && 'minWorkers' in options) {\n    if(options.minWorkers === 'max') {\n      this.minWorkers = this.maxWorkers;\n    } else {\n      validateMinWorkers(options.minWorkers);\n      this.minWorkers = options.minWorkers;\n      this.maxWorkers = Math.max(this.minWorkers, this.maxWorkers);     // in case minWorkers is higher than maxWorkers\n    }\n    this._ensureMinWorkers();\n  }\n\n  this._boundNext = this._next.bind(this);\n\n\n  if (this.workerType === 'thread') {\n    WorkerHandler.ensureWorkerThreads();\n  }\n}\n\n\n/**\n * Execute a function on a worker.\n *\n * Example usage:\n *\n *   var pool = new Pool()\n *\n *   // call a function available on the worker\n *   pool.exec('fibonacci', [6])\n *\n *   // offload a function\n *   function add(a, b) {\n *     return a + b\n *   };\n *   pool.exec(add, [2, 4])\n *       .then(function (result) {\n *         console.log(result); // outputs 6\n *       })\n *       .catch(function(error) {\n *         console.log(error);\n *       });\n *\n * @param {String | Function} method  Function name or function.\n *                                    If `method` is a string, the corresponding\n *                                    method on the worker will be executed\n *                                    If `method` is a Function, the function\n *                                    will be stringified and executed via the\n *                                    workers built-in function `run(fn, args)`.\n * @param {Array} [params]  Function arguments applied when calling the function\n * @param {ExecOptions} [options]  Options object\n * @return {Promise.<*, Error>} result\n */\nPool.prototype.exec = function (method, params, options) {\n  // validate type of arguments\n  if (params && !Array.isArray(params)) {\n    throw new TypeError('Array expected as argument \"params\"');\n  }\n\n  if (typeof method === 'string') {\n    var resolver = Promise.defer();\n\n    if (this.tasks.length >= this.maxQueueSize) {\n      throw new Error('Max queue size of ' + this.maxQueueSize + ' reached');\n    }\n\n    // add a new task to the queue\n    var tasks = this.tasks;\n    var task = {\n      method:  method,\n      params:  params,\n      resolver: resolver,\n      timeout: null,\n      options: options\n    };\n    tasks.push(task);\n\n    // replace the timeout method of the Promise with our own,\n    // which starts the timer as soon as the task is actually started\n    var originalTimeout = resolver.promise.timeout;\n    resolver.promise.timeout = function timeout (delay) {\n      if (tasks.indexOf(task) !== -1) {\n        // task is still queued -> start the timer later on\n        task.timeout = delay;\n        return resolver.promise;\n      }\n      else {\n        // task is already being executed -> start timer immediately\n        return originalTimeout.call(resolver.promise, delay);\n      }\n    };\n\n    // trigger task execution\n    this._next();\n\n    return resolver.promise;\n  }\n  else if (typeof method === 'function') {\n    // send stringified function and function arguments to worker\n    return this.exec('run', [String(method), params]);\n  }\n  else {\n    throw new TypeError('Function or string expected as argument \"method\"');\n  }\n};\n\n/**\n * Create a proxy for current worker. Returns an object containing all\n * methods available on the worker. The methods always return a promise.\n *\n * @return {Promise.<Object, Error>} proxy\n */\nPool.prototype.proxy = function () {\n  if (arguments.length > 0) {\n    throw new Error('No arguments expected');\n  }\n\n  var pool = this;\n  return this.exec('methods')\n      .then(function (methods) {\n        var proxy = {};\n\n        methods.forEach(function (method) {\n          proxy[method] = function () {\n            return pool.exec(method, Array.prototype.slice.call(arguments));\n          }\n        });\n\n        return proxy;\n      });\n};\n\n/**\n * Creates new array with the results of calling a provided callback function\n * on every element in this array.\n * @param {Array} array\n * @param {function} callback  Function taking two arguments:\n *                             `callback(currentValue, index)`\n * @return {Promise.<Array>} Returns a promise which resolves  with an Array\n *                           containing the results of the callback function\n *                           executed for each of the array elements.\n */\n/* TODO: implement map\nPool.prototype.map = function (array, callback) {\n};\n*/\n\n/**\n * Grab the first task from the queue, find a free worker, and assign the\n * worker to the task.\n * @protected\n */\nPool.prototype._next = function () {\n  if (this.tasks.length > 0) {\n    // there are tasks in the queue\n\n    // find an available worker\n    var worker = this._getWorker();\n    if (worker) {\n      // get the first task from the queue\n      var me = this;\n      var task = this.tasks.shift();\n\n      // check if the task is still pending (and not cancelled -> promise rejected)\n      if (task.resolver.promise.pending) {\n        // send the request to the worker\n        var promise = worker.exec(task.method, task.params, task.resolver, task.options)\n          .then(me._boundNext)\n          .catch(function () {\n            // if the worker crashed and terminated, remove it from the pool\n            if (worker.terminated) {\n              return me._removeWorker(worker);\n            }\n          }).then(function() {\n            me._next(); // trigger next task in the queue\n          });\n\n        // start queued timer now\n        if (typeof task.timeout === 'number') {\n          promise.timeout(task.timeout);\n        }\n      } else {\n        // The task taken was already complete (either rejected or resolved), so just trigger next task in the queue\n        me._next();\n      }\n    }\n  }\n};\n\n/**\n * Get an available worker. If no worker is available and the maximum number\n * of workers isn't yet reached, a new worker will be created and returned.\n * If no worker is available and the maximum number of workers is reached,\n * null will be returned.\n *\n * @return {WorkerHandler | null} worker\n * @private\n */\nPool.prototype._getWorker = function() {\n  // find a non-busy worker\n  var workers = this.workers;\n  for (var i = 0; i < workers.length; i++) {\n    var worker = workers[i];\n    if (worker.busy() === false) {\n      return worker;\n    }\n  }\n\n  if (workers.length < this.maxWorkers) {\n    // create a new worker\n    worker = this._createWorkerHandler();\n    workers.push(worker);\n    return worker;\n  }\n\n  return null;\n};\n\n/**\n * Remove a worker from the pool.\n * Attempts to terminate worker if not already terminated, and ensures the minimum\n * pool size is met.\n * @param {WorkerHandler} worker\n * @return {Promise<WorkerHandler>}\n * @protected\n */\nPool.prototype._removeWorker = function(worker) {\n  var me = this;\n\n  DEBUG_PORT_ALLOCATOR.releasePort(worker.debugPort);\n  // _removeWorker will call this, but we need it to be removed synchronously\n  this._removeWorkerFromList(worker);\n  // If minWorkers set, spin up new workers to replace the crashed ones\n  this._ensureMinWorkers();\n  // terminate the worker (if not already terminated)\n  return new Promise(function(resolve, reject) {\n    worker.terminate(false, function(err) {\n      me.onTerminateWorker({\n        forkArgs: worker.forkArgs,\n        forkOpts: worker.forkOpts,\n        workerThreadOpts: worker.workerThreadOpts,\n        script: worker.script\n      });\n      if (err) {\n        reject(err);\n      } else {\n        resolve(worker);\n      }\n    });\n  });\n};\n\n/**\n * Remove a worker from the pool list.\n * @param {WorkerHandler} worker\n * @protected\n */\nPool.prototype._removeWorkerFromList = function(worker) {\n  // remove from the list with workers\n  var index = this.workers.indexOf(worker);\n  if (index !== -1) {\n    this.workers.splice(index, 1);\n  }\n};\n\n/**\n * Close all active workers. Tasks currently being executed will be finished first.\n * @param {boolean} [force=false]   If false (default), the workers are terminated\n *                                  after finishing all tasks currently in\n *                                  progress. If true, the workers will be\n *                                  terminated immediately.\n * @param {number} [timeout]        If provided and non-zero, worker termination promise will be rejected\n *                                  after timeout if worker process has not been terminated.\n * @return {Promise.<void, Error>}\n */\nPool.prototype.terminate = function (force, timeout) {\n  var me = this;\n\n  // cancel any pending tasks\n  this.tasks.forEach(function (task) {\n    task.resolver.reject(new Error('Pool terminated'));\n  });\n  this.tasks.length = 0;\n\n  var f = function (worker) {\n    DEBUG_PORT_ALLOCATOR.releasePort(worker.debugPort);\n    this._removeWorkerFromList(worker);\n  };\n  var removeWorker = f.bind(this);\n\n  var promises = [];\n  var workers = this.workers.slice();\n  workers.forEach(function (worker) {\n    var termPromise = worker.terminateAndNotify(force, timeout)\n      .then(removeWorker)\n      .always(function() {\n        me.onTerminateWorker({\n          forkArgs: worker.forkArgs,\n          forkOpts: worker.forkOpts,\n          workerThreadOpts: worker.workerThreadOpts,\n          script: worker.script\n        });\n      });\n    promises.push(termPromise);\n  });\n  return Promise.all(promises);\n};\n\n/**\n * Retrieve statistics on tasks and workers.\n * @return {{totalWorkers: number, busyWorkers: number, idleWorkers: number, pendingTasks: number, activeTasks: number}} Returns an object with statistics\n */\nPool.prototype.stats = function () {\n  var totalWorkers = this.workers.length;\n  var busyWorkers = this.workers.filter(function (worker) {\n    return worker.busy();\n  }).length;\n\n  return {\n    totalWorkers:  totalWorkers,\n    busyWorkers:   busyWorkers,\n    idleWorkers:   totalWorkers - busyWorkers,\n\n    pendingTasks:  this.tasks.length,\n    activeTasks:   busyWorkers\n  };\n};\n\n/**\n * Ensures that a minimum of minWorkers is up and running\n * @protected\n */\nPool.prototype._ensureMinWorkers = function() {\n  if (this.minWorkers) {\n    for(var i = this.workers.length; i < this.minWorkers; i++) {\n      this.workers.push(this._createWorkerHandler());\n    }\n  }\n};\n\n/**\n * Helper function to create a new WorkerHandler and pass all options.\n * @return {WorkerHandler}\n * @private\n */\nPool.prototype._createWorkerHandler = function () {\n  const overriddenParams = this.onCreateWorker({\n    forkArgs: this.forkArgs,\n    forkOpts: this.forkOpts,\n    workerOpts: this.workerOpts,\n    workerThreadOpts: this.workerThreadOpts,\n    script: this.script\n  }) || {};\n\n  return new WorkerHandler(overriddenParams.script || this.script, {\n    forkArgs: overriddenParams.forkArgs || this.forkArgs,\n    forkOpts: overriddenParams.forkOpts || this.forkOpts,\n    workerOpts: overriddenParams.workerOpts || this.workerOpts,\n    workerThreadOpts: overriddenParams.workerThreadOpts || this.workerThreadOpts,\n    debugPort: DEBUG_PORT_ALLOCATOR.nextAvailableStartingAt(this.debugPortStart),\n    workerType: this.workerType,\n    workerTerminateTimeout: this.workerTerminateTimeout,\n  });\n}\n\n/**\n * Ensure that the maxWorkers option is an integer >= 1\n * @param {*} maxWorkers\n * @returns {boolean} returns true maxWorkers has a valid value\n */\nfunction validateMaxWorkers(maxWorkers) {\n  if (!isNumber(maxWorkers) || !isInteger(maxWorkers) || maxWorkers < 1) {\n    throw new TypeError('Option maxWorkers must be an integer number >= 1');\n  }\n}\n\n/**\n * Ensure that the minWorkers option is an integer >= 0\n * @param {*} minWorkers\n * @returns {boolean} returns true when minWorkers has a valid value\n */\nfunction validateMinWorkers(minWorkers) {\n  if (!isNumber(minWorkers) || !isInteger(minWorkers) || minWorkers < 0) {\n    throw new TypeError('Option minWorkers must be an integer number >= 0');\n  }\n}\n\n/**\n * Test whether a variable is a number\n * @param {*} value\n * @returns {boolean} returns true when value is a number\n */\nfunction isNumber(value) {\n  return typeof value === 'number';\n}\n\n/**\n * Test whether a number is an integer\n * @param {number} value\n * @returns {boolean} Returns true if value is an integer\n */\nfunction isInteger(value) {\n  return Math.round(value) == value;\n}\n\nmodule.exports = Pool;\n", "'use strict';\n\n/**\n * Promise\n *\n * Inspired by https://gist.github.com/RubaXa/8501359 from RubaXa <<EMAIL>>\n *\n * @param {Function} handler   Called as handler(resolve: Function, reject: Function)\n * @param {Promise} [parent]   Parent promise for propagation of cancel and timeout\n */\nfunction Promise(handler, parent) {\n  var me = this;\n\n  if (!(this instanceof Promise)) {\n    throw new SyntaxError('Constructor must be called with the new operator');\n  }\n\n  if (typeof handler !== 'function') {\n    throw new SyntaxError('Function parameter handler(resolve, reject) missing');\n  }\n\n  var _onSuccess = [];\n  var _onFail = [];\n\n  // status\n  this.resolved = false;\n  this.rejected = false;\n  this.pending = true;\n\n  /**\n   * Process onSuccess and onFail callbacks: add them to the queue.\n   * Once the promise is resolve, the function _promise is replace.\n   * @param {Function} onSuccess\n   * @param {Function} onFail\n   * @private\n   */\n  var _process = function (onSuccess, onFail) {\n    _onSuccess.push(onSuccess);\n    _onFail.push(onFail);\n  };\n\n  /**\n   * Add an onSuccess callback and optionally an onFail callback to the Promise\n   * @param {Function} onSuccess\n   * @param {Function} [onFail]\n   * @returns {Promise} promise\n   */\n  this.then = function (onSuccess, onFail) {\n    return new Promise(function (resolve, reject) {\n      var s = onSuccess ? _then(onSuccess, resolve, reject) : resolve;\n      var f = onFail    ? _then(onFail,    resolve, reject) : reject;\n\n      _process(s, f);\n    }, me);\n  };\n\n  /**\n   * Resolve the promise\n   * @param {*} result\n   * @type {Function}\n   */\n  var _resolve = function (result) {\n    // update status\n    me.resolved = true;\n    me.rejected = false;\n    me.pending = false;\n\n    _onSuccess.forEach(function (fn) {\n      fn(result);\n    });\n\n    _process = function (onSuccess, onFail) {\n      onSuccess(result);\n    };\n\n    _resolve = _reject = function () { };\n\n    return me;\n  };\n\n  /**\n   * Reject the promise\n   * @param {Error} error\n   * @type {Function}\n   */\n  var _reject = function (error) {\n    // update status\n    me.resolved = false;\n    me.rejected = true;\n    me.pending = false;\n\n    _onFail.forEach(function (fn) {\n      fn(error);\n    });\n\n    _process = function (onSuccess, onFail) {\n      onFail(error);\n    };\n\n    _resolve = _reject = function () { }\n\n    return me;\n  };\n\n  /**\n   * Cancel te promise. This will reject the promise with a CancellationError\n   * @returns {Promise} self\n   */\n  this.cancel = function () {\n    if (parent) {\n      parent.cancel();\n    }\n    else {\n      _reject(new CancellationError());\n    }\n\n    return me;\n  };\n\n  /**\n   * Set a timeout for the promise. If the promise is not resolved within\n   * the time, the promise will be cancelled and a TimeoutError is thrown.\n   * If the promise is resolved in time, the timeout is removed.\n   * @param {number} delay     Delay in milliseconds\n   * @returns {Promise} self\n   */\n  this.timeout = function (delay) {\n    if (parent) {\n      parent.timeout(delay);\n    }\n    else {\n      var timer = setTimeout(function () {\n        _reject(new TimeoutError('Promise timed out after ' + delay + ' ms'));\n      }, delay);\n\n      me.always(function () {\n        clearTimeout(timer);\n      });\n    }\n\n    return me;\n  };\n\n  // attach handler passing the resolve and reject functions\n  handler(function (result) {\n    _resolve(result);\n  }, function (error) {\n    _reject(error);\n  });\n}\n\n/**\n * Execute given callback, then call resolve/reject based on the returned result\n * @param {Function} callback\n * @param {Function} resolve\n * @param {Function} reject\n * @returns {Function}\n * @private\n */\nfunction _then(callback, resolve, reject) {\n  return function (result) {\n    try {\n      var res = callback(result);\n      if (res && typeof res.then === 'function' && typeof res['catch'] === 'function') {\n        // method returned a promise\n        res.then(resolve, reject);\n      }\n      else {\n        resolve(res);\n      }\n    }\n    catch (error) {\n      reject(error);\n    }\n  }\n}\n\n/**\n * Add an onFail callback to the Promise\n * @param {Function} onFail\n * @returns {Promise} promise\n */\nPromise.prototype['catch'] = function (onFail) {\n  return this.then(null, onFail);\n};\n\n// TODO: add support for Promise.catch(Error, callback)\n// TODO: add support for Promise.catch(Error, Error, callback)\n\n/**\n * Execute given callback when the promise either resolves or rejects.\n * @param {Function} fn\n * @returns {Promise} promise\n */\nPromise.prototype.always = function (fn) {\n  return this.then(fn, fn);\n};\n\n/**\n * Create a promise which resolves when all provided promises are resolved,\n * and fails when any of the promises resolves.\n * @param {Promise[]} promises\n * @returns {Promise} promise\n */\nPromise.all = function (promises){\n  return new Promise(function (resolve, reject) {\n    var remaining = promises.length,\n        results = [];\n\n    if (remaining) {\n      promises.forEach(function (p, i) {\n        p.then(function (result) {\n          results[i] = result;\n          remaining--;\n          if (remaining == 0) {\n            resolve(results);\n          }\n        }, function (error) {\n          remaining = 0;\n          reject(error);\n        });\n      });\n    }\n    else {\n      resolve(results);\n    }\n  });\n};\n\n/**\n * Create a promise resolver\n * @returns {{promise: Promise, resolve: Function, reject: Function}} resolver\n */\nPromise.defer = function () {\n  var resolver = {};\n\n  resolver.promise = new Promise(function (resolve, reject) {\n    resolver.resolve = resolve;\n    resolver.reject = reject;\n  });\n\n  return resolver;\n};\n\n/**\n * Create a cancellation error\n * @param {String} [message]\n * @extends Error\n */\nfunction CancellationError(message) {\n  this.message = message || 'promise cancelled';\n  this.stack = (new Error()).stack;\n}\n\nCancellationError.prototype = new Error();\nCancellationError.prototype.constructor = Error;\nCancellationError.prototype.name = 'CancellationError';\n\nPromise.CancellationError = CancellationError;\n\n\n/**\n * Create a timeout error\n * @param {String} [message]\n * @extends Error\n */\nfunction TimeoutError(message) {\n  this.message = message || 'timeout exceeded';\n  this.stack = (new Error()).stack;\n}\n\nTimeoutError.prototype = new Error();\nTimeoutError.prototype.constructor = Error;\nTimeoutError.prototype.name = 'TimeoutError';\n\nPromise.TimeoutError = TimeoutError;\n\n\nmodule.exports = Promise;\n", "'use strict';\n\nvar Promise = require('./Promise');\nvar environment = require('./environment');\nvar requireFoolWebpack = require('./requireFoolWebpack');\n\n/**\n * Special message sent by parent which causes a child process worker to terminate itself.\n * Not a \"message object\"; this string is the entire message.\n */\nvar TERMINATE_METHOD_ID = '__workerpool-terminate__';\n\nfunction ensureWorkerThreads() {\n  var WorkerThreads = tryRequireWorkerThreads()\n  if (!WorkerThreads) {\n    throw new Error('WorkerPool: workerType = \\'thread\\' is not supported, Node >= 11.7.0 required')\n  }\n\n  return WorkerThreads;\n}\n\n// check whether Worker is supported by the browser\nfunction ensureWebWorker() {\n  // Workaround for a bug in PhantomJS (Or QtWebkit): https://github.com/ariya/phantomjs/issues/14534\n  if (typeof Worker !== 'function' && (typeof Worker !== 'object' || typeof Worker.prototype.constructor !== 'function')) {\n    throw new Error('WorkerPool: Web Workers not supported');\n  }\n}\n\nfunction tryRequireWorkerThreads() {\n  try {\n    return requireFoolWebpack('worker_threads');\n  } catch(error) {\n    if (typeof error === 'object' && error !== null && error.code === 'MODULE_NOT_FOUND') {\n      // no worker_threads available (old version of node.js)\n      return null;\n    } else {\n      throw error;\n    }\n  }\n}\n\n// get the default worker script\nfunction getDefaultWorker() {\n  if (environment.platform === 'browser') {\n    // test whether the browser supports all features that we need\n    if (typeof Blob === 'undefined') {\n      throw new Error('Blob not supported by the browser');\n    }\n    if (!window.URL || typeof window.URL.createObjectURL !== 'function') {\n      throw new Error('URL.createObjectURL not supported by the browser');\n    }\n\n    // use embedded worker.js\n    var blob = new Blob([require('./generated/embeddedWorker')], {type: 'text/javascript'});\n    return window.URL.createObjectURL(blob);\n  }\n  else {\n    // use external worker.js in current directory\n    return __dirname + '/worker.js';\n  }\n}\n\nfunction setupWorker(script, options) {\n  if (options.workerType === 'web') { // browser only\n    ensureWebWorker();\n    return setupBrowserWorker(script, options.workerOpts, Worker);\n  } else if (options.workerType === 'thread') { // node.js only\n    WorkerThreads = ensureWorkerThreads();\n    return setupWorkerThreadWorker(script, WorkerThreads, options.workerThreadOpts);\n  } else if (options.workerType === 'process' || !options.workerType) { // node.js only\n    return setupProcessWorker(script, resolveForkOptions(options), requireFoolWebpack('child_process'));\n  } else { // options.workerType === 'auto' or undefined\n    if (environment.platform === 'browser') {\n      ensureWebWorker();\n      return setupBrowserWorker(script, options.workerOpts, Worker);\n    }\n    else { // environment.platform === 'node'\n      var WorkerThreads = tryRequireWorkerThreads();\n      if (WorkerThreads) {\n        return setupWorkerThreadWorker(script, WorkerThreads, options.workerThreadOpts);\n      } else {\n        return setupProcessWorker(script, resolveForkOptions(options), requireFoolWebpack('child_process'));\n      }\n    }\n  }\n}\n\nfunction setupBrowserWorker(script, workerOpts, Worker) {\n  // create the web worker\n  var worker = new Worker(script, workerOpts);\n\n  worker.isBrowserWorker = true;\n  // add node.js API to the web worker\n  worker.on = function (event, callback) {\n    this.addEventListener(event, function (message) {\n      callback(message.data);\n    });\n  };\n  worker.send = function (message, transfer) {\n    this.postMessage(message, transfer);\n  };\n  return worker;\n}\n\nfunction setupWorkerThreadWorker(script, WorkerThreads, workerThreadOptions) {\n  var worker = new WorkerThreads.Worker(script, {\n    stdout: false, // automatically pipe worker.STDOUT to process.STDOUT\n    stderr: false,  // automatically pipe worker.STDERR to process.STDERR\n    ...workerThreadOptions\n  });\n  worker.isWorkerThread = true;\n  worker.send = function(message, transfer) {\n    this.postMessage(message, transfer);\n  };\n\n  worker.kill = function() {\n    this.terminate();\n    return true;\n  };\n\n  worker.disconnect = function() {\n    this.terminate();\n  };\n\n  return worker;\n}\n\nfunction setupProcessWorker(script, options, child_process) {\n  // no WorkerThreads, fallback to sub-process based workers\n  var worker = child_process.fork(\n    script,\n    options.forkArgs,\n    options.forkOpts\n  );\n\n  // ignore transfer argument since it is not supported by process\n  var send = worker.send;\n  worker.send = function (message) {\n    return send.call(worker, message);\n  };\n\n  worker.isChildProcess = true;\n  return worker;\n}\n\n// add debug flags to child processes if the node inspector is active\nfunction resolveForkOptions(opts) {\n  opts = opts || {};\n\n  var processExecArgv = process.execArgv.join(' ');\n  var inspectorActive = processExecArgv.indexOf('--inspect') !== -1;\n  var debugBrk = processExecArgv.indexOf('--debug-brk') !== -1;\n\n  var execArgv = [];\n  if (inspectorActive) {\n    execArgv.push('--inspect=' + opts.debugPort);\n\n    if (debugBrk) {\n      execArgv.push('--debug-brk');\n    }\n  }\n\n  process.execArgv.forEach(function(arg) {\n    if (arg.indexOf('--max-old-space-size') > -1) {\n      execArgv.push(arg)\n    }\n  })\n\n  return Object.assign({}, opts, {\n    forkArgs: opts.forkArgs,\n    forkOpts: Object.assign({}, opts.forkOpts, {\n      execArgv: (opts.forkOpts && opts.forkOpts.execArgv || [])\n      .concat(execArgv)\n    })\n  });\n}\n\n/**\n * Converts a serialized error to Error\n * @param {Object} obj Error that has been serialized and parsed to object\n * @return {Error} The equivalent Error.\n */\nfunction objectToError (obj) {\n  var temp = new Error('')\n  var props = Object.keys(obj)\n\n  for (var i = 0; i < props.length; i++) {\n    temp[props[i]] = obj[props[i]]\n  }\n\n  return temp\n}\n\n/**\n * A WorkerHandler controls a single worker. This worker can be a child process\n * on node.js or a WebWorker in a browser environment.\n * @param {String} [script] If no script is provided, a default worker with a\n *                          function run will be created.\n * @param {WorkerPoolOptions} _options See docs\n * @constructor\n */\nfunction WorkerHandler(script, _options) {\n  var me = this;\n  var options = _options || {};\n\n  this.script = script || getDefaultWorker();\n  this.worker = setupWorker(this.script, options);\n  this.debugPort = options.debugPort;\n  this.forkOpts = options.forkOpts;\n  this.forkArgs = options.forkArgs;\n  this.workerOpts = options.workerOpts;\n  this.workerThreadOpts = options.workerThreadOpts\n  this.workerTerminateTimeout = options.workerTerminateTimeout;\n\n  // The ready message is only sent if the worker.add method is called (And the default script is not used)\n  if (!script) {\n    this.worker.ready = true;\n  }\n\n  // queue for requests that are received before the worker is ready\n  this.requestQueue = [];\n  this.worker.on('message', function (response) {\n    if (me.terminated) {\n      return;\n    }\n    if (typeof response === 'string' && response === 'ready') {\n      me.worker.ready = true;\n      dispatchQueuedRequests();\n    } else {\n      // find the task from the processing queue, and run the tasks callback\n      var id = response.id;\n      var task = me.processing[id];\n      if (task !== undefined) {\n        if (response.isEvent) {\n          if (task.options && typeof task.options.on === 'function') {\n            task.options.on(response.payload);\n          }\n        } else {\n          // remove the task from the queue\n          delete me.processing[id];\n\n          // test if we need to terminate\n          if (me.terminating === true) {\n            // complete worker termination if all tasks are finished\n            me.terminate();\n          }\n\n          // resolve the task's promise\n          if (response.error) {\n            task.resolver.reject(objectToError(response.error));\n          }\n          else {\n            task.resolver.resolve(response.result);\n          }\n        }\n      }\n    }\n  });\n\n  // reject all running tasks on worker error\n  function onError(error) {\n    me.terminated = true;\n\n    for (var id in me.processing) {\n      if (me.processing[id] !== undefined) {\n        me.processing[id].resolver.reject(error);\n      }\n    }\n    me.processing = Object.create(null);\n  }\n\n  // send all queued requests to worker\n  function dispatchQueuedRequests()\n  {\n    for(const request of me.requestQueue.splice(0)) {\n      me.worker.send(request.message, request.transfer);\n    }\n  }\n\n  var worker = this.worker;\n  // listen for worker messages error and exit\n  this.worker.on('error', onError);\n  this.worker.on('exit', function (exitCode, signalCode) {\n    var message = 'Workerpool Worker terminated Unexpectedly\\n';\n\n    message += '    exitCode: `' + exitCode + '`\\n';\n    message += '    signalCode: `' + signalCode + '`\\n';\n\n    message += '    workerpool.script: `' +  me.script + '`\\n';\n    message += '    spawnArgs: `' +  worker.spawnargs + '`\\n';\n    message += '    spawnfile: `' + worker.spawnfile + '`\\n'\n\n    message += '    stdout: `' + worker.stdout + '`\\n'\n    message += '    stderr: `' + worker.stderr + '`\\n'\n\n    onError(new Error(message));\n  });\n\n  this.processing = Object.create(null); // queue with tasks currently in progress\n\n  this.terminating = false;\n  this.terminated = false;\n  this.cleaning = false;\n  this.terminationHandler = null;\n  this.lastId = 0;\n}\n\n/**\n * Get a list with methods available on the worker.\n * @return {Promise.<String[], Error>} methods\n */\nWorkerHandler.prototype.methods = function () {\n  return this.exec('methods');\n};\n\n/**\n * Execute a method with given parameters on the worker\n * @param {String} method\n * @param {Array} [params]\n * @param {{resolve: Function, reject: Function}} [resolver]\n * @param {ExecOptions}  [options]\n * @return {Promise.<*, Error>} result\n */\nWorkerHandler.prototype.exec = function(method, params, resolver, options) {\n  if (!resolver) {\n    resolver = Promise.defer();\n  }\n\n  // generate a unique id for the task\n  var id = ++this.lastId;\n\n  // register a new task as being in progress\n  this.processing[id] = {\n    id: id,\n    resolver: resolver,\n    options: options\n  };\n\n  // build a JSON-RPC request\n  var request = {\n    message: {\n      id: id,\n      method: method,\n      params: params\n    },\n    transfer: options && options.transfer\n  };\n\n  if (this.terminated) {\n    resolver.reject(new Error('Worker is terminated'));\n  } else if (this.worker.ready) {\n    // send the request to the worker\n    this.worker.send(request.message, request.transfer);\n  } else {\n    this.requestQueue.push(request);\n  }\n\n  // on cancellation, force the worker to terminate\n  var me = this;\n  return resolver.promise.catch(function (error) {\n    if (error instanceof Promise.CancellationError || error instanceof Promise.TimeoutError) {\n      // remove this task from the queue. It is already rejected (hence this\n      // catch event), and else it will be rejected again when terminating\n      delete me.processing[id];\n\n      // terminate worker\n      return me.terminateAndNotify(true)\n        .then(function() {\n          throw error;\n        }, function(err) {\n          throw err;\n        });\n    } else {\n      throw error;\n    }\n  })\n};\n\n/**\n * Test whether the worker is processing any tasks or cleaning up before termination.\n * @return {boolean} Returns true if the worker is busy\n */\nWorkerHandler.prototype.busy = function () {\n  return this.cleaning || Object.keys(this.processing).length > 0;\n};\n\n/**\n * Terminate the worker.\n * @param {boolean} [force=false]   If false (default), the worker is terminated\n *                                  after finishing all tasks currently in\n *                                  progress. If true, the worker will be\n *                                  terminated immediately.\n * @param {function} [callback=null] If provided, will be called when process terminates.\n */\nWorkerHandler.prototype.terminate = function (force, callback) {\n  var me = this;\n  if (force) {\n    // cancel all tasks in progress\n    for (var id in this.processing) {\n      if (this.processing[id] !== undefined) {\n        this.processing[id].resolver.reject(new Error('Worker terminated'));\n      }\n    }\n    this.processing = Object.create(null);\n  }\n\n  if (typeof callback === 'function') {\n    this.terminationHandler = callback;\n  }\n  if (!this.busy()) {\n    // all tasks are finished. kill the worker\n    var cleanup = function(err) {\n      me.terminated = true;\n      me.cleaning = false;\n      if (me.worker != null && me.worker.removeAllListeners) {\n        // removeAllListeners is only available for child_process\n        me.worker.removeAllListeners('message');\n      }\n      me.worker = null;\n      me.terminating = false;\n      if (me.terminationHandler) {\n        me.terminationHandler(err, me);\n      } else if (err) {\n        throw err;\n      }\n    }\n\n    if (this.worker) {\n      if (typeof this.worker.kill === 'function') {\n        if (this.worker.killed) {\n          cleanup(new Error('worker already killed!'));\n          return;\n        }\n\n        // child process and worker threads\n        var cleanExitTimeout = setTimeout(function() {\n          if (me.worker) {\n            me.worker.kill();\n          }\n        }, this.workerTerminateTimeout);\n\n        this.worker.once('exit', function() {\n          clearTimeout(cleanExitTimeout);\n          if (me.worker) {\n            me.worker.killed = true;\n          }\n          cleanup();\n        });\n\n        if (this.worker.ready) {\n          this.worker.send(TERMINATE_METHOD_ID);\n        } else {\n          this.requestQueue.push({ message: TERMINATE_METHOD_ID });\n        }\n\n        // mark that the worker is cleaning up resources\n        // to prevent new tasks from being executed\n        this.cleaning = true;\n        return;\n      }\n      else if (typeof this.worker.terminate === 'function') {\n        this.worker.terminate(); // web worker\n        this.worker.killed = true;\n      }\n      else {\n        throw new Error('Failed to terminate worker');\n      }\n    }\n    cleanup();\n  }\n  else {\n    // we can't terminate immediately, there are still tasks being executed\n    this.terminating = true;\n  }\n};\n\n/**\n * Terminate the worker, returning a Promise that resolves when the termination has been done.\n * @param {boolean} [force=false]   If false (default), the worker is terminated\n *                                  after finishing all tasks currently in\n *                                  progress. If true, the worker will be\n *                                  terminated immediately.\n * @param {number} [timeout]        If provided and non-zero, worker termination promise will be rejected\n *                                  after timeout if worker process has not been terminated.\n * @return {Promise.<WorkerHandler, Error>}\n */\nWorkerHandler.prototype.terminateAndNotify = function (force, timeout) {\n  var resolver = Promise.defer();\n  if (timeout) {\n    resolver.promise.timeout(timeout);\n  }\n  this.terminate(force, function(err, worker) {\n    if (err) {\n      resolver.reject(err);\n    } else {\n      resolver.resolve(worker);\n    }\n  });\n  return resolver.promise;\n};\n\nmodule.exports = WorkerHandler;\nmodule.exports._tryRequireWorkerThreads = tryRequireWorkerThreads;\nmodule.exports._setupProcessWorker = setupProcessWorker;\nmodule.exports._setupBrowserWorker = setupBrowserWorker;\nmodule.exports._setupWorkerThreadWorker = setupWorkerThreadWorker;\nmodule.exports.ensureWorkerThreads = ensureWorkerThreads;\n", "'use strict';\n\nvar MAX_PORTS = 65535;\nmodule.exports = DebugPortAllocator;\nfunction DebugPortAllocator() {\n  this.ports = Object.create(null);\n  this.length = 0;\n}\n\nDebugPortAllocator.prototype.nextAvailableStartingAt = function(starting) {\n  while (this.ports[starting] === true) {\n    starting++;\n  }\n\n  if (starting >= MAX_PORTS) {\n    throw new Error('WorkerPool debug port limit reached: ' + starting + '>= ' + MAX_PORTS );\n  }\n\n  this.ports[starting] = true;\n  this.length++;\n  return starting;\n};\n\nDebugPortAllocator.prototype.releasePort = function(port) {\n  delete this.ports[port];\n  this.length--;\n};\n\n", "var requireFoolWebpack = require('./requireFoolWebpack');\n\n// source: https://github.com/flexdinesh/browser-or-node\nvar isNode = function (nodeProcess) {\n  return (\n    typeof nodeProcess !== 'undefined' &&\n    nodeProcess.versions != null &&\n    nodeProcess.versions.node != null\n  );\n}\nmodule.exports.isNode = isNode\n\n// determines the JavaScript platform: browser or node\nmodule.exports.platform = typeof process !== 'undefined' && isNode(process)\n  ? 'node'\n  : 'browser';\n\n// determines whether the code is running in main thread or not\n// note that in node.js we have to check both worker_thread and child_process\nvar worker_threads = tryRequireFoolWebpack('worker_threads');\nmodule.exports.isMainThread = module.exports.platform === 'node'\n  ? ((!worker_threads || worker_threads.isMainThread) && !process.connected)\n  : typeof Window !== 'undefined';\n\n// determines the number of cpus available\nmodule.exports.cpus = module.exports.platform === 'browser'\n  ? self.navigator.hardwareConcurrency\n  : requireFoolWebpack('os').cpus().length;\n\nfunction tryRequireFoolWebpack (module) {\n  try {\n    return requireFoolWebpack(module);\n  } catch(err) {\n    return null\n  }\n}\n", "/**\n * embeddedWorker.js contains an embedded version of worker.js.\n * This file is automatically generated,\n * changes made in this file will be overwritten.\n */\nmodule.exports = \"!function(){var __webpack_modules__={577:function(e){e.exports=function(e,r){this.message=e,this.transfer=r}}},__webpack_module_cache__={};function __webpack_require__(e){var r=__webpack_module_cache__[e];return void 0!==r||(r=__webpack_module_cache__[e]={exports:{}},__webpack_modules__[e](r,r.exports,__webpack_require__)),r.exports}var __webpack_exports__={};!function(){var exports=__webpack_exports__,__webpack_unused_export__;function _typeof(e){return(_typeof=\\\"function\\\"==typeof Symbol&&\\\"symbol\\\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\\\"function\\\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\\\"symbol\\\":typeof e})(e)}var Transfer=__webpack_require__(577),requireFoolWebpack=eval(\\\"typeof require !== 'undefined' ? require : function (module) { throw new Error('Module \\\\\\\" + module + \\\\\\\" not found.') }\\\"),TERMINATE_METHOD_ID=\\\"__workerpool-terminate__\\\",worker={exit:function(){}},WorkerThreads,parentPort;if(\\\"undefined\\\"!=typeof self&&\\\"function\\\"==typeof postMessage&&\\\"function\\\"==typeof addEventListener)worker.on=function(e,r){addEventListener(e,function(e){r(e.data)})},worker.send=function(e){postMessage(e)};else{if(\\\"undefined\\\"==typeof process)throw new Error(\\\"Script must be executed as a worker\\\");try{WorkerThreads=requireFoolWebpack(\\\"worker_threads\\\")}catch(error){if(\\\"object\\\"!==_typeof(error)||null===error||\\\"MODULE_NOT_FOUND\\\"!==error.code)throw error}WorkerThreads&&null!==WorkerThreads.parentPort?(parentPort=WorkerThreads.parentPort,worker.send=parentPort.postMessage.bind(parentPort),worker.on=parentPort.on.bind(parentPort)):(worker.on=process.on.bind(process),worker.send=function(e){process.send(e)},worker.on(\\\"disconnect\\\",function(){process.exit(1)})),worker.exit=process.exit.bind(process)}function convertError(o){return Object.getOwnPropertyNames(o).reduce(function(e,r){return Object.defineProperty(e,r,{value:o[r],enumerable:!0})},{})}function isPromise(e){return e&&\\\"function\\\"==typeof e.then&&\\\"function\\\"==typeof e.catch}worker.methods={},worker.methods.run=function(e,r){e=new Function(\\\"return (\\\"+e+\\\").apply(null, arguments);\\\");return e.apply(e,r)},worker.methods.methods=function(){return Object.keys(worker.methods)},worker.terminationHandler=void 0,worker.cleanupAndExit=function(e){function r(){worker.exit(e)}if(!worker.terminationHandler)return r();var o=worker.terminationHandler(e);isPromise(o)?o.then(r,r):r()};var currentRequestId=null;worker.on(\\\"message\\\",function(r){if(r===TERMINATE_METHOD_ID)return worker.cleanupAndExit(0);try{var e=worker.methods[r.method];if(!e)throw new Error('Unknown method \\\"'+r.method+'\\\"');currentRequestId=r.id;var o=e.apply(e,r.params);isPromise(o)?o.then(function(e){e instanceof Transfer?worker.send({id:r.id,result:e.message,error:null},e.transfer):worker.send({id:r.id,result:e,error:null}),currentRequestId=null}).catch(function(e){worker.send({id:r.id,result:null,error:convertError(e)}),currentRequestId=null}):(o instanceof Transfer?worker.send({id:r.id,result:o.message,error:null},o.transfer):worker.send({id:r.id,result:o,error:null}),currentRequestId=null)}catch(e){worker.send({id:r.id,result:null,error:convertError(e)})}}),worker.register=function(e,r){if(e)for(var o in e)e.hasOwnProperty(o)&&(worker.methods[o]=e[o]);r&&(worker.terminationHandler=r.onTerminate),worker.send(\\\"ready\\\")},worker.emit=function(e){currentRequestId&&(e instanceof Transfer?worker.send({id:currentRequestId,isEvent:!0,payload:e.message},e.transfer):worker.send({id:currentRequestId,isEvent:!0,payload:e}))},__webpack_unused_export__=worker.register,worker.emit}()}();\";\n", "// source of inspiration: https://github.com/sindresorhus/require-fool-webpack\nvar requireFoolWebpack = eval(\n    'typeof require !== \\'undefined\\' ' +\n    '? require ' +\n    ': function (module) { throw new Error(\\'Module \" + module + \" not found.\\') }'\n);\n\nmodule.exports = requireFoolWebpack;\n", "/**\n * The helper class for transferring data from the worker to the main thread.\n *\n * @param {Object} message The object to deliver to the main thread.\n * @param {Object[]} transfer An array of transferable Objects to transfer ownership of.\n */\nfunction Transfer(message, transfer) {\n  this.message = message;\n  this.transfer = transfer;\n}\n\nmodule.exports = Transfer;\n", "/**\n * worker must be started as a child process or a web worker.\n * It listens for RPC messages from the parent process.\n */\nvar Transfer = require('./transfer');\n\n// source of inspiration: https://github.com/sindresorhus/require-fool-webpack\nvar requireFoolWebpack = eval(\n    'typeof require !== \\'undefined\\'' +\n    ' ? require' +\n    ' : function (module) { throw new Error(\\'Module \" + module + \" not found.\\') }'\n);\n\n/**\n * Special message sent by parent which causes the worker to terminate itself.\n * Not a \"message object\"; this string is the entire message.\n */\nvar TERMINATE_METHOD_ID = '__workerpool-terminate__';\n\n// var nodeOSPlatform = require('./environment').nodeOSPlatform;\n\n// create a worker API for sending and receiving messages which works both on\n// node.js and in the browser\nvar worker = {\n  exit: function() {}\n};\nif (typeof self !== 'undefined' && typeof postMessage === 'function' && typeof addEventListener === 'function') {\n  // worker in the browser\n  worker.on = function (event, callback) {\n    addEventListener(event, function (message) {\n      callback(message.data);\n    })\n  };\n  worker.send = function (message) {\n    postMessage(message);\n  };\n}\nelse if (typeof process !== 'undefined') {\n  // node.js\n\n  var WorkerThreads;\n  try {\n    WorkerThreads = requireFoolWebpack('worker_threads');\n  } catch(error) {\n    if (typeof error === 'object' && error !== null && error.code === 'MODULE_NOT_FOUND') {\n      // no worker_threads, fallback to sub-process based workers\n    } else {\n      throw error;\n    }\n  }\n\n  if (WorkerThreads &&\n    /* if there is a parentPort, we are in a WorkerThread */\n    WorkerThreads.parentPort !== null) {\n    var parentPort  = WorkerThreads.parentPort;\n    worker.send = parentPort.postMessage.bind(parentPort);\n    worker.on = parentPort.on.bind(parentPort);\n    worker.exit = process.exit.bind(process);\n  } else {\n    worker.on = process.on.bind(process);\n    // ignore transfer argument since it is not supported by process\n    worker.send = function (message) {\n      process.send(message);\n    };\n    // register disconnect handler only for subprocess worker to exit when parent is killed unexpectedly\n    worker.on('disconnect', function () {\n      process.exit(1);\n    });\n    worker.exit = process.exit.bind(process);\n  }\n}\nelse {\n  throw new Error('Script must be executed as a worker');\n}\n\nfunction convertError(error) {\n  return Object.getOwnPropertyNames(error).reduce(function(product, name) {\n    return Object.defineProperty(product, name, {\n\tvalue: error[name],\n\tenumerable: true\n    });\n  }, {});\n}\n\n/**\n * Test whether a value is a Promise via duck typing.\n * @param {*} value\n * @returns {boolean} Returns true when given value is an object\n *                    having functions `then` and `catch`.\n */\nfunction isPromise(value) {\n  return value && (typeof value.then === 'function') && (typeof value.catch === 'function');\n}\n\n// functions available externally\nworker.methods = {};\n\n/**\n * Execute a function with provided arguments\n * @param {String} fn     Stringified function\n * @param {Array} [args]  Function arguments\n * @returns {*}\n */\nworker.methods.run = function run(fn, args) {\n  var f = new Function('return (' + fn + ').apply(null, arguments);');\n  return f.apply(f, args);\n};\n\n/**\n * Get a list with methods available on this worker\n * @return {String[]} methods\n */\nworker.methods.methods = function methods() {\n  return Object.keys(worker.methods);\n};\n\n/**\n * Custom handler for when the worker is terminated.\n */\nworker.terminationHandler = undefined;\n\n/**\n * Cleanup and exit the worker.\n * @param {Number} code \n * @returns \n */\nworker.cleanupAndExit = function(code) {\n  var _exit = function() {\n    worker.exit(code);\n  }\n\n  if(!worker.terminationHandler) {\n    return _exit();\n  }\n\n  var result = worker.terminationHandler(code);\n  if (isPromise(result)) {\n    result.then(_exit, _exit);\n  } else {\n    _exit();\n  }\n}\n\nvar currentRequestId = null;\n\nworker.on('message', function (request) {\n  if (request === TERMINATE_METHOD_ID) {\n    return worker.cleanupAndExit(0);\n  }\n  try {\n    var method = worker.methods[request.method];\n\n    if (method) {\n      currentRequestId = request.id;\n      \n      // execute the function\n      var result = method.apply(method, request.params);\n\n      if (isPromise(result)) {\n        // promise returned, resolve this and then return\n        result\n            .then(function (result) {\n              if (result instanceof Transfer) {\n                worker.send({\n                  id: request.id,\n                  result: result.message,\n                  error: null\n                }, result.transfer);\n              } else {\n                worker.send({\n                  id: request.id,\n                  result: result,\n                  error: null\n                });\n              }\n              currentRequestId = null;\n            })\n            .catch(function (err) {\n              worker.send({\n                id: request.id,\n                result: null,\n                error: convertError(err)\n              });\n              currentRequestId = null;\n            });\n      }\n      else {\n        // immediate result\n        if (result instanceof Transfer) {\n          worker.send({\n            id: request.id,\n            result: result.message,\n            error: null\n          }, result.transfer);\n        } else {\n          worker.send({\n            id: request.id,\n            result: result,\n            error: null\n          });\n        }\n\n        currentRequestId = null;\n      }\n    }\n    else {\n      throw new Error('Unknown method \"' + request.method + '\"');\n    }\n  }\n  catch (err) {\n    worker.send({\n      id: request.id,\n      result: null,\n      error: convertError(err)\n    });\n  }\n});\n\n/**\n * Register methods to the worker\n * @param {Object} [methods]\n * @param {WorkerRegisterOptions} [options]\n */\nworker.register = function (methods, options) {\n\n  if (methods) {\n    for (var name in methods) {\n      if (methods.hasOwnProperty(name)) {\n        worker.methods[name] = methods[name];\n      }\n    }\n  }\n\n  if (options) {\n    worker.terminationHandler = options.onTerminate;\n  }\n\n  worker.send('ready');\n};\n\nworker.emit = function (payload) {\n  if (currentRequestId) {\n    if (payload instanceof Transfer) {\n      worker.send({\n        id: currentRequestId,\n        isEvent: true,\n        payload: payload.message\n      }, payload.transfer);\n      return;\n    }\n\n    worker.send({\n      id: currentRequestId,\n      isEvent: true,\n      payload\n    });\n  }\n};\n\nif (typeof exports !== 'undefined') {\n  exports.add = worker.register;\n  exports.emit = worker.emit;\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "var environment = require('./environment');\n\n/**\n * Create a new worker pool\n * @param {string} [script]\n * @param {WorkerPoolOptions} [options]\n * @returns {Pool} pool\n */\nexports.pool = function pool(script, options) {\n  var Pool = require('./Pool');\n\n  return new Pool(script, options);\n};\n\n/**\n * Create a worker and optionally register a set of methods to the worker.\n * @param {Object} [methods]\n * @param {WorkerRegisterOptions} [options]\n */\nexports.worker = function worker(methods, options) {\n  var worker = require('./worker');\n  worker.add(methods, options);\n};\n\n/**\n * Sends an event to the parent worker pool.\n * @param {any} payload \n */\nexports.workerEmit = function workerEmit(payload) {\n  var worker = require('./worker');\n  worker.emit(payload);\n};\n\n/**\n * Create a promise.\n * @type {Promise} promise\n */\nexports.Promise = require('./Promise');\n\n/**\n * Create a transfer object.\n * @type {Transfer} transfer\n */\nexports.Transfer = require('./transfer');\n\nexports.platform = environment.platform;\nexports.isMainThread = environment.isMainThread;\nexports.cpus = environment.cpus;"], "names": ["Promise", "require", "Worker<PERSON><PERSON>ler", "environment", "DebugPortAllocator", "DEBUG_PORT_ALLOCATOR", "Pool", "script", "options", "workers", "tasks", "forkArgs", "Object", "freeze", "forkOpts", "workerOpts", "workerThreadOpts", "debugPortStart", "nodeWorker", "workerType", "maxQueueSize", "Infinity", "workerTerminateTimeout", "onCreateWorker", "onTerminateWorker", "validateMaxWorkers", "maxWorkers", "Math", "max", "cpus", "minWorkers", "validateMinWorkers", "_ensureMinWorkers", "_boundNext", "_next", "bind", "ensureWorkerThreads", "prototype", "exec", "method", "params", "Array", "isArray", "TypeError", "resolver", "defer", "length", "Error", "task", "timeout", "push", "originalTimeout", "promise", "delay", "indexOf", "call", "String", "proxy", "arguments", "pool", "then", "methods", "for<PERSON>ach", "slice", "worker", "_get<PERSON><PERSON><PERSON>", "me", "shift", "pending", "terminated", "_remove<PERSON><PERSON>ker", "i", "busy", "_createWorkerHandler", "releasePort", "debugPort", "_removeWorkerFromList", "resolve", "reject", "terminate", "err", "index", "splice", "force", "f", "removeW<PERSON>ker", "promises", "termPromise", "terminateAndNotify", "always", "all", "stats", "totalWorkers", "busyWorkers", "filter", "idleWorkers", "pendingTasks", "activeTasks", "overriddenParams", "nextAvailableStartingAt", "isNumber", "isInteger", "value", "round", "module", "exports", "handler", "parent", "SyntaxError", "_onSuccess", "_onFail", "resolved", "rejected", "_process", "onSuccess", "onFail", "s", "_then", "_resolve", "result", "fn", "_reject", "error", "cancel", "CancellationError", "timer", "setTimeout", "TimeoutError", "clearTimeout", "callback", "res", "remaining", "results", "p", "message", "stack", "constructor", "name", "_createForOfIteratorHelper", "o", "allowArrayLike", "it", "Symbol", "iterator", "_unsupportedIterableToArray", "F", "n", "done", "e", "_e", "normalCompletion", "didErr", "step", "next", "_e2", "minLen", "_arrayLikeToArray", "toString", "from", "test", "arr", "len", "arr2", "ownKeys", "r", "t", "keys", "getOwnPropertySymbols", "getOwnPropertyDescriptor", "enumerable", "apply", "_objectSpread", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "key", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurable", "writable", "arg", "_toPrimitive", "_typeof", "input", "hint", "prim", "toPrimitive", "undefined", "Number", "requireFoolWebpack", "TERMINATE_METHOD_ID", "WorkerThreads", "tryRequireWorkerThreads", "ensureWebWorker", "Worker", "code", "getDefaultWorker", "platform", "Blob", "window", "URL", "createObjectURL", "blob", "type", "__dirname", "setupWorker", "setupBrowserWorker", "setupWorkerThreadWorker", "setupProcessWorker", "resolveForkOptions", "isBrowserWorker", "on", "event", "addEventListener", "data", "send", "transfer", "postMessage", "workerThreadOptions", "stdout", "stderr", "isWorkerThread", "kill", "disconnect", "child_process", "fork", "isChildProcess", "opts", "processExecArgv", "process", "execArgv", "join", "inspectorActive", "debugBrk", "assign", "concat", "objectToError", "temp", "props", "_options", "ready", "requestQueue", "response", "dispatchQueuedRequests", "id", "processing", "isEvent", "payload", "terminating", "onError", "create", "_iterator", "_step", "request", "exitCode", "signalCode", "spawnargs", "spawnfile", "cleaning", "<PERSON><PERSON><PERSON><PERSON>", "lastId", "cleanup", "removeAllListeners", "killed", "cleanExitTimeout", "once", "_tryRequireWorkerThreads", "_setupProcessWorker", "_setupBrowserWorker", "_setupWorkerThreadWorker", "MAX_PORTS", "ports", "starting", "port", "isNode", "nodeProcess", "versions", "node", "worker_threads", "tryRequireFoolWebpack", "isMainThread", "connected", "Window", "self", "navigator", "hardwareConcurrency", "eval", "Transfer", "exit", "parentPort", "convertError", "getOwnPropertyNames", "reduce", "product", "isPromise", "run", "args", "Function", "cleanupAndExit", "_exit", "currentRequestId", "register", "hasOwnProperty", "onTerminate", "emit", "add", "workerEmit"], "sourceRoot": ""}