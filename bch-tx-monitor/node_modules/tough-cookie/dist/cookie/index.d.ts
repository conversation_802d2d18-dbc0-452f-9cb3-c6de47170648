export { MemoryCookieStore, type MemoryCookieStoreIndex } from '../memstore';
export { pathMatch } from '../pathMatch';
export { permuteDomain } from '../permuteDomain';
export { getPublicSuffix, type GetPublicSuffixOptions, } from '../getPublicSuffix';
export { Store } from '../store';
export { ParameterError } from '../validators';
export { version } from '../version';
export { type Callback, type ErrorCallback, type Nullable } from '../utils';
export { canonicalDomain } from './canonicalDomain';
export { PrefixSecurityEnum, type SerializedCookie, type SerializedCookieJar, } from './constants';
export { Cookie, type CreateCookieOptions, type ParseCookieOptions, } from './cookie';
export { cookieCompare } from './cookieCompare';
export { CookieJar, type CreateCookieJarOptions, type GetCookiesOptions, type SetCookieOptions, } from './cookieJar';
export { defaultPath } from './defaultPath';
export { domainMatch } from './domainMatch';
export { formatDate } from './formatDate';
export { parseDate } from './parseDate';
export { permutePath } from './permutePath';
import { Cookie, ParseCookieOptions } from './cookie';
/**
 * {@inheritDoc Cookie.parse}
 * @public
 */
export declare function parse(str: string, options?: ParseCookieOptions): Cookie | undefined;
/**
 * {@inheritDoc Cookie.fromJSON}
 * @public
 */
export declare function fromJSON(str: unknown): Cookie | undefined;
