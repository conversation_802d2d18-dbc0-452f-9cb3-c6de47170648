# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [2.0.0](https://www.github.com/yargs/yargs-unparser/compare/v1.6.4...v2.0.0) (2020-10-02)


### ⚠ BREAKING CHANGES

* upgrade deps drop Node 6/8 (#71)

### Code Refactoring

* upgrade deps drop Node 6/8 ([#71](https://www.github.com/yargs/yargs-unparser/issues/71)) ([c686882](https://www.github.com/yargs/yargs-unparser/commit/c686882f5ad554be44169e3745e741cb4ec898d0))

### [1.6.4](https://www.github.com/yargs/yargs-unparser/compare/v1.6.3...v1.6.4) (2020-10-01)


### Bug Fixes

* **security:** upgraded flat to version ^5.0.2 ([9bd7c67](https://www.github.com/yargs/yargs-unparser/commit/9bd7c672e12417319c5d4de79070d9c7cd5107f2))

### [1.6.3](https://www.github.com/yargs/yargs-unparser/compare/v1.6.2...v1.6.3) (2020-06-17)


### Bug Fixes

* test automatic publish ([209a487](https://www.github.com/yargs/yargs-unparser/commit/209a4870af799f4b200c2a89d7b7e50c9fd5fd1f))

### [1.6.2](https://www.github.com/yargs/yargs-unparser/compare/v1.6.1...v1.6.2) (2020-06-17)


### Bug Fixes

* **readme:** marketing was injected dubiously into README ([#60](https://www.github.com/yargs/yargs-unparser/issues/60)) ([1167667](https://www.github.com/yargs/yargs-unparser/commit/1167667886fcb103c747e3c9855f353ee0e41c03))

### [1.6.1](https://www.github.com/yargs/yargs-unparser/compare/v1.6.0...v1.6.1) (2020-06-17)


### Bug Fixes

* **deps:** downgrade yargs, such that we continue supporting Node 6 ([#57](https://www.github.com/yargs/yargs-unparser/issues/57)) ([f69406c](https://www.github.com/yargs/yargs-unparser/commit/f69406c34bead63011590f7b51a24a6f311c1a48))

## 1.6.0 (2019-07-30)


### Bug Fixes

* **security:** update deps addressing recent audit vulnerabilities ([#40](https://github.com/yargs/yargs-unparser/issues/40)) ([2e74f1b](https://github.com/yargs/yargs-unparser/commit/2e74f1b))
* address bug with camelCased flattened keys ([#32](https://github.com/yargs/yargs-unparser/issues/32)) ([981533a](https://github.com/yargs/yargs-unparser/commit/981533a))
* **deps:** updated the lodash version to fix vulnerability ([#39](https://github.com/yargs/yargs-unparser/issues/39)) ([7375966](https://github.com/yargs/yargs-unparser/commit/7375966))
* **package:** update yargs to version 10.0.3 ([f1eb4cb](https://github.com/yargs/yargs-unparser/commit/f1eb4cb))
* **package:** update yargs to version 11.0.0 ([6aa7c91](https://github.com/yargs/yargs-unparser/commit/6aa7c91))


### Features

* add interoperation with minimist ([ba477f5](https://github.com/yargs/yargs-unparser/commit/ba477f5))

<a name="1.5.0"></a>
# 1.5.0 (2018-11-30)


### Bug Fixes

* **package:** update yargs to version 10.0.3 ([f1eb4cb](https://github.com/yargs/yargs-unparser/commit/f1eb4cb))
* **package:** update yargs to version 11.0.0 ([6aa7c91](https://github.com/yargs/yargs-unparser/commit/6aa7c91))


### Features

* add interoperation with minimist ([ba477f5](https://github.com/yargs/yargs-unparser/commit/ba477f5))



<a name="1.4.0"></a>
# [1.4.0](https://github.com/moxystudio/yargs-unparser/compare/v1.3.0...v1.4.0) (2017-12-30)


### Features

* add interoperation with minimist ([ba477f5](https://github.com/moxystudio/yargs-unparser/commit/ba477f5))
