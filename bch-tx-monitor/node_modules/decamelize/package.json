{"name": "decamelize", "version": "4.0.0", "description": "Convert a camelized string into a lowercased one with a custom separator: unicorn<PERSON>ain<PERSON> → unicorn_rainbow", "license": "MIT", "repository": "sindresorhus/decamelize", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["decamelize", "decamelcase", "camelcase", "lowercase", "case", "dash", "hyphen", "string", "text", "convert"], "devDependencies": {"ava": "^2.4.0", "tsd": "^0.11.0", "xo": "^0.24.0"}}