# Cryptocurrency Transaction Monitor Test Runner

This repository contains test runners for all cryptocurrency transaction monitors in your workspace.

## Overview

The test runners automatically discover and test all cryptocurrency monitor projects in directories starting with:
- `bch*` (Bitcoin Cash)
- `btc*` (Bitcoin)
- `eth*` (Ethereum)
- `ltc*` (Litecoin)
- `nem*` (NEM)

## Available Test Runners

### 1. Node.js Test Runner (Recommended)
```bash
# Run all tests using Node.js
node run-all-tests.js

# Or using npm
npm test
```

### 2. Bash Test Runner (Unix/Linux/macOS)
```bash
# Run all tests using bash script
./run-all-tests.sh

# Or using npm
npm run test:bash
```

## Features

- 🔍 **Auto-discovery**: Automatically finds all crypto monitor directories
- 📦 **Dependency Management**: Installs npm dependencies before testing
- 🎨 **Colored Output**: Easy-to-read colored console output
- 📊 **Summary Report**: Shows passed/failed projects summary
- ⚡ **Fast Execution**: Runs tests in parallel where possible
- 🛡️ **Error Handling**: Graceful error handling with detailed output

## Output Example

```
🚀 Cryptocurrency Transaction Monitor Test Runner
=================================================

🔍 Scanning for cryptocurrency monitor directories...
Found 5 cryptocurrency monitor directories:
   - BCH-tx-monitor
   - btc-tx-monitor
   - eth-tx-monitor
   - ltc-tx-monitor
   - nem-tx-monitor

📁 Testing: BCH-tx-monitor
   Directory: BCH-tx-monitor
   Installing dependencies...
   ✅ Dependencies installed
   Running tests...
   ✅ Tests PASSED
   1 passing (2ms)

📊 TEST SUMMARY
===============
Total Projects: 5
Passed: 5
Failed: 0

✅ PASSED PROJECTS:
   ✓ BCH-tx-monitor
   ✓ btc-tx-monitor
   ✓ eth-tx-monitor
   ✓ ltc-tx-monitor
   ✓ nem-tx-monitor

🎉 All tests passed successfully!
```

## Requirements

- Node.js 14.0.0 or higher
- npm (comes with Node.js)
- Each crypto monitor project must have:
  - `package.json` with a test script
  - Valid npm dependencies

## Project Structure

```
tx-mon/
├── BCH-tx-monitor/          # Bitcoin Cash monitor
├── btc-tx-monitor/          # Bitcoin monitor
├── eth-tx-monitor/          # Ethereum monitor
├── ltc-tx-monitor/          # Litecoin monitor
├── nem-tx-monitor/          # NEM monitor
├── run-all-tests.js         # Node.js test runner
├── run-all-tests.sh         # Bash test runner
├── package.json             # Test runner configuration
└── README.md                # This file
```

## Troubleshooting

### Common Issues

1. **"No test script defined"**: Make sure each project has a test script in package.json
2. **"Dependencies failed to install"**: Check network connection and npm registry access
3. **"Directory not found"**: Ensure directory names start with supported prefixes

### Manual Testing

To test individual projects manually:
```bash
cd BCH-tx-monitor
npm install
npm test
```

## Contributing

When adding new cryptocurrency monitors:
1. Name the directory starting with the crypto prefix (bch*, btc*, etc.)
2. Include a proper `package.json` with test script
3. Add appropriate test files
4. The test runner will automatically discover and test your project

## License

ISC
