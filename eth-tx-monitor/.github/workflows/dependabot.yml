name: Dependabot Status

on:
  pull_request:
    paths:
      - 'package-lock.json'
      - 'requirements.txt'
  push:
    branches:
      - main

jobs:
  dependabot:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '14'

      - name: Install dependencies
        run: npm install

      - name: Run tests
        run: npm test
