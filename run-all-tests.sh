#!/bin/bash

# Cryptocurrency Transaction Monitor Test Runner
# Runs tests for all crypto monitor folders (bch*, btc*, nem*, eth*, ltc*)

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
TOTAL_PROJECTS=0
PASSED_PROJECTS=0
FAILED_PROJECTS=0

# Arrays to track results
PASSED_DIRS=()
FAILED_DIRS=()

echo -e "${BLUE}🚀 Cryptocurrency Transaction Monitor Test Runner${NC}"
echo -e "${BLUE}=================================================${NC}"
echo ""

# Function to run tests in a directory
run_tests_in_dir() {
    local dir=$1
    local project_name=$(basename "$dir")
    
    echo -e "${YELLOW}📁 Testing: $project_name${NC}"
    echo "   Directory: $dir"
    
    if [ ! -d "$dir" ]; then
        echo -e "   ${RED}❌ Directory not found${NC}"
        return 1
    fi
    
    if [ ! -f "$dir/package.json" ]; then
        echo -e "   ${RED}❌ No package.json found${NC}"
        return 1
    fi
    
    # Check if test script exists
    if ! grep -q '"test"' "$dir/package.json"; then
        echo -e "   ${RED}❌ No test script defined in package.json${NC}"
        return 1
    fi
    
    # Change to directory and run tests
    cd "$dir"
    
    echo "   Installing dependencies..."
    if npm install --silent > /dev/null 2>&1; then
        echo -e "   ${GREEN}✅ Dependencies installed${NC}"
    else
        echo -e "   ${RED}❌ Failed to install dependencies${NC}"
        cd - > /dev/null
        return 1
    fi
    
    echo "   Running tests..."
    if npm test > test_output.log 2>&1; then
        echo -e "   ${GREEN}✅ Tests PASSED${NC}"
        # Show test summary
        if grep -q "passing\|passed" test_output.log; then
            grep -E "(passing|passed|✓)" test_output.log | head -3 | sed 's/^/   /'
        fi
        rm -f test_output.log
        cd - > /dev/null
        return 0
    else
        echo -e "   ${RED}❌ Tests FAILED${NC}"
        echo "   Error details:"
        tail -10 test_output.log | sed 's/^/   /'
        rm -f test_output.log
        cd - > /dev/null
        return 1
    fi
}

# Find all directories that match crypto patterns
echo "🔍 Scanning for cryptocurrency monitor directories..."
CRYPTO_DIRS=()

# Look for directories starting with crypto prefixes
for pattern in "bch*" "btc*" "nem*" "eth*" "ltc*"; do
    for dir in $pattern; do
        if [ -d "$dir" ] && [ "$dir" != "." ] && [ "$dir" != ".." ]; then
            CRYPTO_DIRS+=("$dir")
        fi
    done
done

if [ ${#CRYPTO_DIRS[@]} -eq 0 ]; then
    echo -e "${RED}❌ No cryptocurrency monitor directories found${NC}"
    echo "   Looking for directories starting with: bch*, btc*, nem*, eth*, ltc*"
    exit 1
fi

echo -e "${GREEN}Found ${#CRYPTO_DIRS[@]} cryptocurrency monitor directories:${NC}"
for dir in "${CRYPTO_DIRS[@]}"; do
    echo "   - $dir"
done
echo ""

# Run tests for each directory
for dir in "${CRYPTO_DIRS[@]}"; do
    TOTAL_PROJECTS=$((TOTAL_PROJECTS + 1))
    
    if run_tests_in_dir "$dir"; then
        PASSED_PROJECTS=$((PASSED_PROJECTS + 1))
        PASSED_DIRS+=("$dir")
    else
        FAILED_PROJECTS=$((FAILED_PROJECTS + 1))
        FAILED_DIRS+=("$dir")
    fi
    
    echo ""
done

# Summary
echo -e "${BLUE}📊 TEST SUMMARY${NC}"
echo -e "${BLUE}===============${NC}"
echo -e "Total Projects: $TOTAL_PROJECTS"
echo -e "${GREEN}Passed: $PASSED_PROJECTS${NC}"
echo -e "${RED}Failed: $FAILED_PROJECTS${NC}"
echo ""

if [ ${#PASSED_DIRS[@]} -gt 0 ]; then
    echo -e "${GREEN}✅ PASSED PROJECTS:${NC}"
    for dir in "${PASSED_DIRS[@]}"; do
        echo -e "   ${GREEN}✓${NC} $dir"
    done
    echo ""
fi

if [ ${#FAILED_DIRS[@]} -gt 0 ]; then
    echo -e "${RED}❌ FAILED PROJECTS:${NC}"
    for dir in "${FAILED_DIRS[@]}"; do
        echo -e "   ${RED}✗${NC} $dir"
    done
    echo ""
fi

# Exit with appropriate code
if [ $FAILED_PROJECTS -eq 0 ]; then
    echo -e "${GREEN}🎉 All tests passed successfully!${NC}"
    exit 0
else
    echo -e "${RED}💥 Some tests failed. Check the output above for details.${NC}"
    exit 1
fi
